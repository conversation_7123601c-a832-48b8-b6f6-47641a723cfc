import React from "react";
import { <PERSON>rowserRouter as Router, Routes, Route } from "react-router-dom";
import NavBar from "./components/NavBar";
import HomePage from "./pages/Home";
import AboutPage from "./pages/About";
import ProPage from "./pages/ProPage";
import WidgetPage from "./pages/Widget";
import "./App.css";

function App() {
	return (
		<Router>
			<div className="App">
				<NavBar />
				<Routes>
					<Route path="/" element={<HomePage />} />
					<Route path="/propage" element={<ProPage />} />
					<Route path="/about" element={<AboutPage />} />
					<Route path="/widget" element={<WidgetPage />} />
				</Routes>
			</div>
		</Router>
	);
}

export default App;
