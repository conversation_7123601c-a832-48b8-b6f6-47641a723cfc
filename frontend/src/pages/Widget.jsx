import React, { useEffect, useState, useRef } from "react";
import WidgetTable from "../components/WidgetTable";
import { Link } from "react-router-dom";

function WidgetPage() {
  const [generatedHtml, setGeneratedHtml] = useState("");
  const [showCopyButton, setShowCopyButton] = useState(false);
  const widgetContainerRef = useRef(null);

  useEffect(() => {
    document.title = "PolyVol Widget";
  }, []);

  const generateHtml = () => {
    if (widgetContainerRef.current) {
      const htmlContent = widgetContainerRef.current.outerHTML;
      setGeneratedHtml(htmlContent);
      setShowCopyButton(true);
    }
  };

  const copyHtml = () => {
    navigator.clipboard.writeText(generatedHtml)
      .then(() => {
        alert("HTML copied to clipboard!");
      })
      .catch(err => {
        console.error("Failed to copy: ", err);
        alert("Failed to copy HTML. Please try again.");
      });
  };

  return (
    <div className="container-fluid">
      <div className="row">
        <div className="col-md-12">
          <div className="container">
            <div className="row">
              <div className="col-md-12 text-end mt-3">
                <h6 className="txt-green">
                  <Link to="/" className="txt-green">Home</Link> | <Link to="/about" className="txt-green">About</Link>
                </h6>
              </div>

              <div className="col-md-12 banner mt-4 mb-1">
                <div className="text-center">
                  <h1 className="mb-4 txt-green d-flex align-items-center justify-content-center">
                    <img width="100" src="/volcano.png" alt="" />
                    <span>PolyVol Widget</span>
                  </h1>
                  <h2 className="txt-green">
                    Customizable market data widget
                  </h2>
                </div>
              </div>
            </div>

            <div className="widget-container" ref={widgetContainerRef}>
              <WidgetTable />
            </div>

            <div className="text-center mt-4">
              <button 
                className="btn btn-success" 
                onClick={generateHtml}
              >
                Generate HTML
              </button>
            </div>

            {showCopyButton && (
              <div className="mt-4">
                <div className="d-flex justify-content-center mb-3">
                  <button 
                    className="btn btn-primary" 
                    onClick={copyHtml}
                  >
                    Copy HTML
                  </button>
                </div>
                <div className="border p-3 bg-light">
                  <pre style={{ whiteSpace: 'pre-wrap', maxHeight: '300px', overflow: 'auto' }}>
                    {generatedHtml}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default WidgetPage;