import React, { useEffect, useState, useRef } from "react";
import WidgetTable from "../components/WidgetTable";
import { Link } from "react-router-dom";

function WidgetPage() {
  const [generatedHtml, setGeneratedHtml] = useState("");
  const [showCopyButton, setShowCopyButton] = useState(false);
  const widgetContainerRef = useRef(null);

  useEffect(() => {
    document.title = "PolyVol Widget";
  }, []);

  const generateHtml = () => {
    if (widgetContainerRef.current) {
      // Create a clean HTML structure with just the tables
      let htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PolyVol Widget Tables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .widget-blue { color: #1552f0 !important; }
        .widget-blue a { color: #1552f0 !important; }
        .widget-blue th { color: #1552f0 !important; }
        .widget-blue h1, .widget-blue h2, .widget-blue h3, .widget-blue h6 { color: #1552f0 !important; }
        .widget-blue .txt-green { color: #1552f0 !important; }
        .widget-blue .text-success { color: #1552f0 !important; }
        .table-responsive { margin-bottom: 2rem; }
        h3 { margin-bottom: 1rem; }
        body { padding: 20px; }
        .text-center { text-align: center; }
        .text-capitalize { text-transform: capitalize; }
    </style>
</head>
<body>`;

      // Find and add volatility table
      const volatilityHeaders = Array.from(widgetContainerRef.current.querySelectorAll('th')).filter(th =>
        th.textContent.includes('24h Volatility')
      );

      if (volatilityHeaders.length > 0) {
        const volatilityTable = volatilityHeaders[0].closest('table');
        if (volatilityTable) {
          htmlContent += `
    <div class="mb-5 widget-blue">
        <h3 class="txt-green">Top 10 Markets by Volatility</h3>
        <div class="table-responsive">
            ${volatilityTable.outerHTML}
        </div>
    </div>`;
        }
      }

      // Find and add volume table
      const volumeHeaders = Array.from(widgetContainerRef.current.querySelectorAll('th')).filter(th =>
        th.textContent.includes('24h Volume')
      );

      if (volumeHeaders.length > 0) {
        const volumeTable = volumeHeaders[0].closest('table');
        if (volumeTable) {
          htmlContent += `
    <div class="mb-5 widget-blue">
        <h3 class="txt-green">Top 10 Markets by Volume</h3>
        <div class="table-responsive">
            ${volumeTable.outerHTML}
        </div>
    </div>`;
        }
      }

      // If no tables were found, show a message
      if (!volatilityHeaders.length && !volumeHeaders.length) {
        htmlContent += `
    <div class="alert alert-warning">
        <h4>No tables found</h4>
        <p>Please make sure to enable "Show Top 10 Volatility" or "Show Top 10 Volume" checkboxes to display tables.</p>
    </div>`;
      }

      htmlContent += `
</body>
</html>`;

      setGeneratedHtml(htmlContent);
      setShowCopyButton(true);
    }
  };

  const copyHtml = () => {
    navigator.clipboard.writeText(generatedHtml)
      .then(() => {
        alert("HTML copied to clipboard!");
      })
      .catch(err => {
        console.error("Failed to copy: ", err);
        alert("Failed to copy HTML. Please try again.");
      });
  };

  return (
    <div className="container-fluid widget-blue">
      <div className="row">
        <div className="col-md-12">
          <div className="container">
            <div className="row">
              <div className="col-md-12 text-end mt-3">
                <h6 className="txt-green">
                  <Link to="/" className="txt-green">Home</Link> | <Link to="/about" className="txt-green">About</Link>
                </h6>
              </div>

              <div className="col-md-12 banner mt-4 mb-1">
                <div className="text-center">
                  <h1 className="mb-4 txt-green d-flex align-items-center justify-content-center">
                    <img width="100" src="/volcano.png" alt="" />
                    <span>PolyVol Widget</span>
                  </h1>
                  <h2 className="txt-green">
                    Customizable market data widget
                  </h2>
                </div>
              </div>
            </div>

            <div className="widget-container" ref={widgetContainerRef}>
              <WidgetTable />
            </div>

            <div className="text-center mt-4">
              <button 
                className="btn btn-success" 
                onClick={generateHtml}
              >
                Generate HTML
              </button>
            </div>

            {showCopyButton && (
              <div className="mt-4">
                <div className="d-flex justify-content-center mb-3">
                  <button 
                    className="btn btn-primary" 
                    onClick={copyHtml}
                  >
                    Copy HTML
                  </button>
                </div>
                <div className="border p-3 bg-light">
                  <pre style={{ whiteSpace: 'pre-wrap', maxHeight: '300px', overflow: 'auto' }}>
                    {generatedHtml}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default WidgetPage;