import React, { useEffect, useState } from "react";
import MarketTable from "../components/MarketTable";
import { api, fetchData } from "../api/api";
import { Link } from "react-router-dom";

function HomePage() {
  const timer = 300;
  const [counter, setCounter] = useState(timer);
  const [marketData, setMarketData] = useState([]);
  const [hotData, setHotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    document.title = "PolyVol";
  }, []);

  const fetchNewData = async () => {
    setLoading(true);
    setError(null);

    try {
      const marketResult = await fetchData("markets");
      if (marketResult) {
        setMarketData(marketResult);
        console.log("Market Response:", marketResult);
      } else {
        throw new Error("Market data is empty");
      }
    } catch (error) {
      console.error("Error fetching market data:", error);
      setError("Failed to fetch market data.");
    }

    try {
      const hotResult = await fetchData("top-5-moves");
      if (hotResult) {
        setHotData(hotResult);
        console.log("Hot Response:", hotResult);
      } else {
        throw new Error("Hot data is empty");
      }
    } catch (error) {
      console.error("Error fetching hot data:", error);
      setError((prev) =>
        prev
          ? prev + " Also, failed to fetch hot data."
          : "Failed to fetch hot data."
      );
    }

    setLoading(false);
  };

  //   const fetchNewData = async () => {
  //     setLoading(true);

  //     try {
  //       //   // Fetch markets data
  //       //   const marketResponse = await fetch(
  //       //   //       //     `${BASE_URL}/markets`
  //       //   );
  //       //   if (!marketResponse.ok) throw new Error("Failed to fetch market data");
  //       //   const marketResult = await marketResponse.json();
  //       //   setMarketData(marketResult);

  //       //   console.log("marketResponse", marketResult);

  //       const marketResponse = await api.get("/markets");
  //       if (!marketResponse.ok) throw new Error("Failed to fetch market data");
  //       const marketResult = await marketResponse.json();
  //       setMarketData(marketResult);
  //       console.log("marketResponse", marketResult);

  //       // Fetch hot data
  //       const hotResponse = await fetch(
  //         // "https://d48f-39-34-99-3.ngrok-free.app/top-5-moves"
  //           //       );
  //       if (!hotResponse.ok) throw new Error("Failed to fetch hot data");
  //       const hotResult = await hotResponse.json();
  //       setHotData(hotResult);

  //       setCounter(300); // Reset counter
  //     } catch (error) {
  //       console.error("Error fetching data:", error);
  //       setError(error.message || "An error occurred");
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  useEffect(() => {
    fetchNewData(); // Initial fetch
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCounter((prev) => prev - 1);
    }, 1000);

    if (counter === 0) {
      fetchNewData();
      setCounter(timer);
    }

    return () => clearInterval(interval);
  }, [counter]);

  return (
    <div className="container mb-5">
      {error && (
        <div className="alert alert-danger text-center mt-3">{error}</div>
      )}

      {loading ? (
        <div className="text-center mt-5">
          <div className="spinner-border text-success" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3 dark-loading">Loading data...</p>
        </div>
      ) : (
        <div className="row">
          <div className="col">
            <div className="row mt-4 mb-4">
              <div className="d-flex align-items-center labels-div justify-content-end col-md-12">
                <h6 className="me-5 txt-light">
                  Active Markets{" "}
                  <span className="text-success txt-green">
                    {marketData.length}
                  </span>
                </h6>
                <h6 className="txt-light">
                  Next Update{" "}
                  <span className="text-success txt-green">
                    {Math.floor(counter / 60)}:
                    {String(counter % 60).padStart(2, "0")}
                  </span>
                </h6>
                <h6 className="txt-light ms-4">
                  <Link to="about">About polyvol.info</Link>
                </h6>
                <h6 className="txt-light ms-4">
                  <Link to="/widget">Widget</Link>
                </h6>
              </div>

              <div className="col-md-12 banner mt-4 mb-1">
                <div className="text-center">
                  <h1 className="mb-4 txt-green d-flex align-items-center justify-content-center">
                    <img width="100" src="/volcano.png" alt="" />
                    <span>PolyVol.info </span>
                  </h1>
                  <h2 className="txt-green">
                    {" "}
                    What’s moving now on Polymarket
                  </h2>
                </div>
              </div>
            </div>

            {/* <div className="text-center mt-4">
              <Link to="/propage">
                <button className="button-pro">Go to ProPage</button>
              </Link>
            </div> */}

            <div className="market-tabl">
              <MarketTable markets={marketData} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default HomePage;
