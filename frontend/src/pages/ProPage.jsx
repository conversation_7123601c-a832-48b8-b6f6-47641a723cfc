import React, { useEffect, useState } from "react";
import ProMarketTable from "../components/ProMarketTable";
import { api, fetchData } from "../api/api";
import { Link } from "react-router-dom";

function HomePage() {
  const timer = 300;
  const [counter, setCounter] = useState(timer);
  const [marketData, setMarketData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    document.title = "PolyVol";
  }, []);

  const fetchNewData = async () => {
    setLoading(true);
    setError(null);

    try {
      const marketResult = await fetchData("pro_markets");
      if (marketResult) {
        setMarketData(marketResult);
        console.log("Pro Market Response:", marketResult);
      } else {
        throw new Error("Pro Market data is empty");
      }
    } catch (error) {
      console.error("Error fetching pro market data:", error);
      setError("Failed to fetch pro market data.");
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchNewData(); // Initial fetch
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCounter((prev) => prev - 1);
    }, 1000);

    if (counter === 0) {
      fetchNewData();
      setCounter(timer);
    }

    return () => clearInterval(interval);
  }, [counter]);

  return (
    <div className="container mb-5">
      {error && (
        <div className="alert alert-danger text-center mt-3">{error}</div>
      )}

      {loading ? (
        <div className="text-center mt-5">
          <div className="spinner-border text-success" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3 dark-loading">Loading data...</p>
        </div>
      ) : (
        <div className="row">
          <div className="col">
            <div className="row mt-4 mb-4">
              <div className="d-flex align-items-center labels-div justify-content-end col-md-12">
                <h6 className="me-5 txt-light">
                  Active Markets{" "}
                  <span className="text-success txt-green">
                    {marketData.length}
                  </span>
                </h6>
                <h6 className="txt-light">
                  Next Update{" "}
                  <span className="text-success txt-green">
                    {Math.floor(counter / 60)}:
                    {String(counter % 60).padStart(2, "0")}
                  </span>
                </h6>
                <h6 className="txt-light ms-4">
                  <Link to="about">About polyvol.info</Link>
                </h6>
              </div>

              <div className="col-md-12 banner mt-4 mb-1">
                <div className="text-center">
                  <h1 className="mb-4 txt-green d-flex align-items-center justify-content-center">
                    <img width="100" src="/volcano.png" alt="" />
                    <span>PolyVol.info </span>
                  </h1>
                  <h2 className="txt-green">
                    {" "}
                    What’s moving now on Polymarket Pro
                  </h2>
                </div>
              </div>
            </div>
            {/* 
            <div className="text-center mt-4">
              <Link to="/">
                <button className="button-pro">Go to HomePage</button>
              </Link>
            </div> */}
            <div className="market-tabl">
              <ProMarketTable markets={marketData} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default HomePage;
