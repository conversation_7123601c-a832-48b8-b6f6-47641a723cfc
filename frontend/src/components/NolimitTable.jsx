import { useState, useMemo } from 'react';

function MarketNoLimitTable({ markets }) {
    const [limit, setLimit] = useState('all');
    const [sortConfig, setSortConfig] = useState({ key: null, direction: 'ascending' });

    const sortedMarkets = useMemo(() => {
        let sortableMarkets = [...markets];

        if (sortConfig.key) {
            sortableMarkets.sort((a, b) => {
                if (a[sortConfig.key] < b[sortConfig.key]) {
                    return sortConfig.direction === 'ascending' ? -1 : 1;
                }
                if (a[sortConfig.key] > b[sortConfig.key]) {
                    return sortConfig.direction === 'ascending' ? 1 : -1;
                }
                return 0;
            });
        }

        if (limit === 'all') {
            return sortableMarkets;
        }
        return sortableMarkets.slice(0, limit);
    }, [markets, limit, sortConfig]);

    const requestSort = (key) => {
        let direction = 'ascending';
        if (sortConfig.key === key && sortConfig.direction === 'ascending') {
            direction = 'descending';
        }
        setSortConfig({ key, direction });
    };

    return (
        <div style={{ marginTop: "4%" }}>
            <p className='txt-green'><i>🔮 Markets sorted by 24 hour volatility</i></p>

            <div className="table-responsive">
                <table className="table table-bordered table-striped table-hover">
                    <thead>
                        <tr className='text-center'>
                            <th className='txt-green py-3 ps-2' style={{ width: '550px' }} scope="col">Market</th>
                            <th scope="col" className='txt-green py-3'>Odds</th>
                            <th scope="col" className='txt-green py-3' onClick={() => requestSort('volume')} style={{ cursor: 'pointer' }}>
                                24h Volume {sortConfig.key === 'volume' ? (sortConfig.direction === 'ascending' ? '🔼' : '🔽') : ''}
                            </th>
                            <th scope="col" className='txt-green py-3' onClick={() => requestSort('volatility')} style={{ cursor: 'pointer' }}>
                                24h Volatility {sortConfig.key === 'volatility' ? (sortConfig.direction === 'ascending' ? '🔼' : '🔽') : ''}
                            </th>
                            <th scope="col" className='txt-green py-3' onClick={() => requestSort('imp')} style={{ cursor: 'pointer' }}>
                                <span style={{ marginLeft: "8%" }}>🚨 {sortConfig.key === 'imp' ? (sortConfig.direction === 'ascending' ? '🔼' : '🔽') : ''}</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {
                            sortedMarkets.map((obj) => (
                                <tr key={obj.market_slug}>
                                    <td><a className="text-success txt-green" rel="noreferrer" target='_blank' href={`https://polymarket.com/event/${obj.slg}`}><b>{obj.question}</b></a></td>
                                    <td className='text-center'>{Math.round(obj.odds * 100)}%</td>
                                    <td className='text-center'>${Math.round(obj.volume)}</td>
                                    <td className='text-center'>{obj.volatility}</td>
                                    <td className='text-center'>{Math.round(obj.imp)}</td>
                                </tr>
                            ))
                        }
                    </tbody>
                </table>
            </div>
        </div>
    );
}

export default MarketNoLimitTable;
