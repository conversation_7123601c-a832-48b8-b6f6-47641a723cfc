import { useState, useEffect } from 'react';
import { api } from '../api/api';

function WidgetTable() {
  const [volatilityData, setVolatilityData] = useState([]);
  const [volumeData, setVolumeData] = useState([]);
  const [showVolatility, setShowVolatility] = useState(true);
  const [showVolume, setShowVolume] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filterSpread, setFilterSpread] = useState(true); // Added spread filter, default true

  // Widget blue color override styles
  const widgetStyles = `
    .widget-container h1,
    .widget-container h2,
    .widget-container h3,
    .widget-container h4,
    .widget-container h5,
    .widget-container h6 {
      color: #1552f0 !important;
    }
    .widget-container table a,
    .widget-container table a:hover,
    .widget-container table a:visited,
    .widget-container table a:active {
      color: #1552f0 !important;
    }
    .widget-container th,
    .widget-container td {
      color: #1552f0 !important;
    }
    .widget-container table .txt-green,
    .widget-container table .text-success,
    .widget-container table .text-primary,
    .widget-container table .text-info {
      color: #1552f0 !important;
    }
    .widget-container .table > :not(caption) > * > * {
      color: #1552f0 !important;
    }
    .widget-container .table-striped > tbody > tr:nth-of-type(odd) > * {
      color: #1552f0 !important;
    }
    .widget-container .dark-mode .table > :not(caption) > * > * {
      color: #1552f0 !important;
    }
    .widget-container .dark-mode .table-striped > tbody > tr:nth-of-type(odd) > * {
      color: #1552f0 !important;
    }
    /* Keep filter labels in appropriate colors for both modes */
    .widget-container .category-filters label,
    .widget-container .category-filters span {
      color: #333 !important; /* Dark text for light mode */
    }
    .dark-mode .widget-container .category-filters label,
    .dark-mode .widget-container .category-filters span {
      color: #fff !important; /* White text for dark mode */
    }
    /* Widget data text - same as filters */
    .widget-container > p {
      color: #333 !important; /* Dark text for light mode */
    }
    .dark-mode .widget-container > p {
      color: #fff !important; /* White text for dark mode - same as filters */
    }
  `;

  // Category filter state (same as MarketTable)
  const [selectedCategories, setSelectedCategories] = useState({
    sports: false,
    politics: true,
    business: true,
    culture: true,
    crypto: false,
    tweets: false,
    weather: false,
    other: true,
  });
  const [filterVolume, setFilterVolume] = useState(true);

  // Category filter handler
  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  // Backend now handles all filtering, so we can use the data directly
  const filteredVolatilityData = volatilityData;
  const filteredVolumeData = volumeData;

  const fetchVolatilityData = async () => {
    if (!showVolatility) return;

    setLoading(true);
    setError(null);

    try {
      // Get selected categories
      const selectedCats = Object.keys(selectedCategories)
        .filter(key => selectedCategories[key]);

      // Build API URL with categories and filter parameters
      let url = '/max_volatility?limit=10';
      if (selectedCats.length > 0) {
        url += `&categories=${selectedCats.join(',')}`;
      }
      // Add filter parameters
      url += `&filter_volume=${filterVolume}`;
      url += `&filter_spread=${!filterSpread}`; // Invert the value

      console.log('Fetching volatility data with URL:', url);
      const response = await api.get(url);

      if (response.data?.top_volatility_markets) {
        // Backend now handles all filtering, so we can use the data directly
        setVolatilityData(response.data.top_volatility_markets);
      } else {
        setError("Volatility data is missing in response");
      }
    } catch (err) {
      console.error('Error fetching volatility data:', err);
      setError('Failed to fetch volatility data');
    } finally {
      setLoading(false);
    }
  };

  const fetchVolumeData = async () => {
    if (!showVolume) return;

    setLoading(true);
    setError(null);

    try {
      // Get selected categories
      const selectedCats = Object.keys(selectedCategories)
        .filter(key => selectedCategories[key]);

      // Build API URL with categories and filter parameters
      let url = '/max_volume?limit=10';
      if (selectedCats.length > 0) {
        url += `&categories=${selectedCats.join(',')}`;
      }
      // Add filter parameters
      url += `&filter_volume=${filterVolume}`;
      url += `&filter_spread=${!filterSpread}`; // Invert the value

      console.log('Fetching volume data with URL:', url);
      const response = await api.get(url);

      if (response.data?.top_volume_markets) {
        // Backend now handles all filtering, so we can use the data directly
        setVolumeData(response.data.top_volume_markets);
      } else {
        setError("Volume data is missing in response");
      }
    } catch (err) {
      console.error('Error fetching volume data:', err);
      setError('Failed to fetch volume data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (showVolatility) fetchVolatilityData();
  }, [showVolatility, filterSpread, filterVolume, selectedCategories]); // Added filterVolume dependency

  useEffect(() => {
    if (showVolume) fetchVolumeData();
  }, [showVolume, filterSpread, filterVolume, selectedCategories]); // Added filterVolume dependency  dbgfd hgdymfd vcgfnhfghgdfgdgrdngdtretergtetf jfhfgdgdgd g

  const handleVolatilityChange = () => {
    setShowVolatility(!showVolatility);
  };

  const handleVolumeChange = () => {
    setShowVolume(!showVolume);
  };

  const handleSpreadChange = () => {
    setFilterSpread(!filterSpread);
  };

  return (
    <div className="widget-container" style={{ marginTop: '4%' }}>
      <style dangerouslySetInnerHTML={{ __html: widgetStyles }} />
      <p>
        <i>🔮 Widget data from Polymarket</i>
      </p>

      <div className='category-filters mb-3'>
        <label>
          <input
            type='checkbox'
            checked={showVolatility}
            onChange={handleVolatilityChange}
          />
          <span className='ps-2'>Show Top 10 Volatility</span>
        </label>
        <label className='ms-3'>
          <input
            type='checkbox'
            checked={showVolume}
            onChange={handleVolumeChange}
          />
          <span className='ps-2'>Show Top 10 Volume</span>
        </label>
        <label className='ms-3'>
          <input
            type='checkbox'
            checked={filterSpread}
            onChange={handleSpreadChange}
          />
          <span className='ps-2'>Show Spread &lt; 0.2</span>
        </label>
      </div>

      {/* Category Filters */}
      <div className="category-filters mb-3">
        <label>Filter:</label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.sports}
            onChange={() => handleCategoryChange("sports")}
          />
          <span className="ps-2">Sports</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.politics}
            onChange={() => handleCategoryChange("politics")}
          />
          <span className="ps-2">Politics</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.business}
            onChange={() => handleCategoryChange("business")}
          />
          <span className="ps-2">Business</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.culture}
            onChange={() => handleCategoryChange("culture")}
          />
          <span className="ps-2">Culture</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.crypto}
            onChange={() => handleCategoryChange("crypto")}
          />
          <span className="ps-2">Crypto</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.tweets}
            onChange={() => handleCategoryChange("tweets")}
          />
          <span className="ps-2">Tweets</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.weather}
            onChange={() => handleCategoryChange("weather")}
          />
          <span className="ps-2">Weather</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={selectedCategories.other}
            onChange={() => handleCategoryChange("other")}
          />
          <span className="ps-2">Other</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={filterVolume}
            onChange={() => setFilterVolume((prev) => !prev)}
          />
          <span className="ps-2">Exclude Markets with Volume &lt; $1000</span>
        </label>
      </div>

      {loading && <p className="text-center">Loading data...</p>}
      {error && <p className="text-center text-danger">{error}</p>}

      {showVolatility && filteredVolatilityData.length > 0 && (
        <div className='mb-5'>
          <h3>Top Markets by Volatility</h3>
          <div className='table-responsive'>
            <table className='table table-bordered table-striped table-hover'>
              <thead>
                <tr className='text-center'>
                  <th className='py-3 ps-2' style={{ width: '450px' }} scope='col'>
                    Market
                  </th>
                  <th scope='col' className='py-3'>Category</th>
                  <th scope='col' className='py-3'>Odds</th>
                  <th scope='col' className='py-3'>24h Volatility</th>
                  <th scope='col' className='py-3'>Date</th>
                </tr>
              </thead>
              <tbody>
                {filteredVolatilityData.map((obj, index) => (
                  <tr key={index}>
                    <td>
                      <a
                        rel='noreferrer'
                        target='_blank'
                        href={`https://polymarket.com/event/${obj.slg}`}
                      >
                        <b>{obj.question}</b>
                      </a>
                    </td>
                    <td className='text-center text-capitalize'>{obj.classification || 'Other'}</td>
                    <td className='text-center'>{Math.round(obj.odds * 100)}%</td>
                    <td className='text-center'>{obj.volatility.toFixed(4)}</td>
                    <td className='text-center'>
                      {obj.createdAt
                        ? new Date(obj.createdAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })
                        : 'N/A'
                      }
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
                    
      {showVolume && filteredVolumeData.length > 0 && (
      <div>
        <h3>Top Markets by Volume</h3>
        <div className='table-responsive'>
          <table className='table table-bordered table-striped table-hover'>
            <thead>
              <tr className='text-center'>
                <th className='py-3 ps-2' style={{ width: '450px' }} scope='col'>
                  Market
                </th>
                <th scope='col' className='py-3'>Category</th>
                <th scope='col' className='py-3'>Odds</th>
                <th scope='col' className='py-3'>24h Volume</th>
                <th scope='col' className='py-3'>Date</th>
              </tr>
            </thead>
            <tbody>
              {filteredVolumeData.map((obj, index) => (
                <tr key={index}>
                  <td>
                    <a
                      rel='noreferrer'
                      target='_blank'
                      href={`https://polymarket.com/event/${obj.slg}`}
                    >
                      <b>{obj.question}</b>
                    </a>
                  </td>
                  <td className='text-center text-capitalize'>{obj.classification || 'Other'}</td>
                  <td className='text-center'>{Math.round(obj.odds * 100)}%</td>
                  <td className='text-center'>${Math.round(parseFloat(obj.volume)).toLocaleString()}</td>
                  <td className='text-center'>
                    {obj.createdAt
                      ? new Date(obj.createdAt).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })
                      : 'N/A'
                    }
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    )}
    </div>
  );
}


export default WidgetTable;
