import React from 'react';

function TopCard({ title, percentChange, url }) {
  return (
    <div className='col-md-3'>
      <div className="card">
        <div className="card-body">
          {/* Add a title attribute for the tooltip */}
          <h5
            style={{ fontSize: "15px", whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}
            className="card-title txt-light"
            title={title} // Tooltip shows the full title
          >
            {title}
          </h5>
          <h6
            className={`card-subtitle mb-2 mt-2 ${percentChange > 15 ? 'text-success txt-green' : 'text-danger txt-red'}`}
          >
            {percentChange}% {percentChange > 15 ? '⬆️' : '⬇️'}
          </h6>
          <a rel="noreferrer" target="_blank" href={url} className="card-link text-success txt-green">
            View
          </a>
        </div>
      </div>
    </div>
  );
}

export default TopCard;
