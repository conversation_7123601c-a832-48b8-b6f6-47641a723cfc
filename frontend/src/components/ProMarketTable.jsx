import { useState, useEffect, useMemo } from "react";

function ProMarketTable({ markets }) {
  const [limit, setLimit] = useState("all");
  const [sortConfig, setSortConfig] = useState({
    key: "volatility",
    direction: "descending",
  });
  const [selectedCategories, setSelectedCategories] = useState({
    sports: false,
    politics: true,
    business: true,
    culture: true,
    crypto: true,
    tweets: true,
    weather: true,
    other: true,
  });
  const [filterVolume, setFilterVolume] = useState(true);
  const [filterSpread, setFilterSpread] = useState(false);

  const [displayedMarkets, setDisplayedMarkets] = useState([]);

  const symbolToApiField = {
    "💰": "moneybag",
    "🐂": "ox",
    "🐻": "bear",
  };

  const handleCategoryChange = (category) => {
    setSelectedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const filteredMarkets = useMemo(() => {
    let sortableMarkets = [...markets];

    const selectedKeys = Object.keys(selectedCategories).filter(
      (key) => selectedCategories[key]
    );

    sortableMarkets = sortableMarkets.filter((market) => {
      const marketCategory = market.classification?.toLowerCase();
      if (
        selectedCategories.other &&
        (!marketCategory ||
          ![
            "sports",
            "politics",
            "business",
            "culture",
            "crypto",
            "tweets",
            "weather",
          ].includes(marketCategory))
      ) {
        return true;
      }

      return marketCategory && selectedKeys.includes(marketCategory);
    });

    if (filterVolume) {
      sortableMarkets = sortableMarkets.filter(
        (market) => parseFloat(market.volume) >= 1000
      );
    }
    if (filterSpread) {
      sortableMarkets = sortableMarkets.filter(
        (market) => parseFloat(market.spread) > 0.2
      );
    }

    sortableMarkets = sortableMarkets.filter(
      (obj) => Math.abs(obj.odds - 1) > 0.01 && Math.abs(obj.odds - 0) > 0.01
    );

    if (sortConfig.key) {
      sortableMarkets.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        if (sortConfig.key === "imp") {
          aValue = parseFloat(a.volume) * (a.volatility || 0);
          bValue = parseFloat(b.volume) * (b.volatility || 0);
        }

        if (["💰", "🐂", "🐻"].includes(sortConfig.key)) {
          const field = symbolToApiField[sortConfig.key];
          aValue = a[field];
          bValue = b[field];
        }

        if (["volume", "volatility"].includes(sortConfig.key)) {
          aValue = parseFloat(aValue);
          bValue = parseFloat(bValue);
        }

        if (aValue < bValue) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }

    return limit === "all" ? sortableMarkets : sortableMarkets.slice(0, limit);
  }, [
    markets,
    limit,
    sortConfig,
    selectedCategories,
    filterVolume,
    filterSpread,
  ]);

  useEffect(() => {
    setDisplayedMarkets(filteredMarkets);
  }, [filteredMarkets]);

  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  const [darkMode, setDarkMode] = useState(() => {
    const savedMode = localStorage.getItem("darkMode");
    return savedMode ? JSON.parse(savedMode) : true;
  });

  useEffect(() => {
    const handleDarkModeChange = (event) => {
      const isDarkMode = event.detail;
      setDarkMode(isDarkMode);
      if (isDarkMode) {
        setLimit("all");
      } else {
        setLimit(50);
      }
    };

    window.addEventListener("darkModeChange", handleDarkModeChange);
    return () => {
      window.removeEventListener("darkModeChange", handleDarkModeChange);
    };
  }, []);

  return (
    <div style={{ marginTop: "4%" }}>
      <p className="txt-green">
        <i>🔮 Markets sorted by 24 hour volatility</i>
      </p>

      <div className="category-filters mb-3">
        <label>Sort:</label>
        {Object.keys(selectedCategories).map((category) => (
          <label key={category}>
            <input
              type="checkbox"
              checked={selectedCategories[category]}
              onChange={() => handleCategoryChange(category)}
            />
            <span className="ps-2">
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </span>
          </label>
        ))}
        <label>
          <input
            type="checkbox"
            checked={filterVolume}
            onChange={() => setFilterVolume((prev) => !prev)}
          />
          <span className="ps-2">Exclude Markets with Volume &lt; $1000</span>
        </label>
        <label>
          <input
            type="checkbox"
            checked={filterSpread}
            onChange={() => setFilterSpread((prev) => !prev)}
          />
          <span className="ps-2">Show Spread &gt; 0.2</span>
        </label>
      </div>

      {!darkMode && (
        <div className="mb-3 row g-4 table-filters">
          <div className="col-md-3 d-flex align-items-center justify-content-start">
            <label className="me-2 txt-green">Limit Records:</label>
            <select
              className="form-select w-auto"
              value={limit}
              onChange={(e) =>
                setLimit(
                  e.target.value === "all" ? "all" : parseInt(e.target.value)
                )
              }
            >
              <option value={50}>50</option>
              <option value={150}>150</option>
              <option value={250}>250</option>
              <option value={350}>350</option>
              <option value={500}>500</option>
              <option value="all">All</option>
            </select>
          </div>
        </div>
      )}

      <div className="table-responsive">
        <table className="table table-bordered table-striped table-hover">
          <thead>
            <tr className="text-center">
              <th className="txt-green py-3 ps-2" style={{ width: "550px" }}>
                Market
              </th>
              <th scope="col" className="txt-green py-3">
                Odds
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                style={{ width: "140px" }}
              >
                Odds Trend
              </th>
              <th scope="col" className="txt-green py-3">
                Category
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                onClick={() => requestSort("volume")}
                style={{ cursor: "pointer" }}
              >
                Volume{" "}
                {sortConfig.key === "volume" &&
                  (sortConfig.direction === "ascending" ? "🔼" : "🔽")}
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                onClick={() => requestSort("spread")}
                style={{ cursor: "pointer" }}
              >
                Spread{" "}
                {sortConfig.key === "spread" &&
                  (sortConfig.direction === "ascending" ? "🔼" : "🔽")}
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                onClick={() => requestSort("volatility")}
                style={{ cursor: "pointer" }}
              >
                Volatility{" "}
                {sortConfig.key === "volatility" &&
                  (sortConfig.direction === "ascending" ? "🔼" : "🔽")}
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                onClick={() => requestSort("imp")}
                style={{ cursor: "pointer" }}
              >
                Impact{" "}
                {sortConfig.key === "imp" &&
                  (sortConfig.direction === "ascending" ? "🔼" : "🔽")}
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                onClick={() => requestSort("💰")}
                style={{ cursor: "pointer" }}
              >
                💰{" "}
                {sortConfig.key === "💰" &&
                  (sortConfig.direction === "ascending" ? "🔼" : "🔽")}
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                onClick={() => requestSort("🐂")}
                style={{ cursor: "pointer" }}
              >
                🐂{" "}
                {sortConfig.key === "🐂" &&
                  (sortConfig.direction === "ascending" ? "🔼" : "🔽")}
              </th>
              <th
                scope="col"
                className="txt-green py-3"
                onClick={() => requestSort("🐻")}
                style={{ cursor: "pointer" }}
              >
                🐻{" "}
                {sortConfig.key === "🐻" &&
                  (sortConfig.direction === "ascending" ? "🔼" : "🔽")}
              </th>
            </tr>
          </thead>
          <tbody>
            {displayedMarkets.map((market, index) => {
              const impactValue = parseFloat(market.volume) * (market.volatility || 0);
              return (
                <tr key={index} className="text-center">
                  <td className="text-start ps-2">
                    <a
                      className="text-success txt-green"
                      href={`https://polymarket.com/event/${market.slg}`}
                      target="_blank"
                      rel="noreferrer"
                    >
                      <b>{market.question}</b>
                    </a>
                  </td>
                  <td>{Math.round(market.odds * 100)}%</td>
                  <td>{market.odds_trend}</td>
                  <td>{market.classification}</td>
                  <td>${parseFloat(market.volume).toLocaleString()}</td>
                  <td>{market.spread}</td>
                  <td>{market.volatility.toFixed(3)}</td>
                  <td>{impactValue.toFixed(0)}</td>
                  <td>{market.moneybag ? "✅" : ""}</td>
                  <td>
                    {market.ox && market.ox.length > 0
                      ? market.ox.join(", ")
                      : ""}
                  </td>
                  <td>
                    {market.bear && market.bear.length > 0
                      ? market.bear.join(", ")
                      : ""}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default ProMarketTable; 