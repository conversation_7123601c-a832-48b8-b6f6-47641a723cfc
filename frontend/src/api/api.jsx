import axios from "axios";

const BASE_URL = process.env.REACT_APP_API_BASE_URL;

const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": true,
  },
});

// Generic function
const fetchData = async (endpoint) => {
  try {
    console.log(`Fetching from: ${BASE_URL}${endpoint}`); // Debug log
    const response = await api.get(endpoint);
    return response.data;
  } catch (error) {
    console.error(
      `API Fetch Error: ${error.response?.status} - ${
        error.response?.statusText || error.message
      }`
    );
    return null;
  }
};

// export default api;
export { api, fetchData };
