#!/usr/bin/env python3
"""
Script to fetch response from Polymarket API and save it to response.json
"""

import httpx
import json
import asyncio
from datetime import datetime

# Base API URL for Polymarket
BASE_API_URL = "https://gamma-api.polymarket.com/events"

async def fetch_and_save_response():
    """
    Fetch all markets from Polymarket API and save the response to response.json
    """
    print("Starting API fetch...")
    print(f"Fetching from: {BASE_API_URL}")
    
    async with httpx.AsyncClient() as client:
        all_markets = []
        offset = 0
        limit = 500
        batch_count = 0
        
        try:
            while True:
                batch_count += 1
                url = f"{BASE_API_URL}?limit={limit}&offset={offset}&closed=false"
                
                print(f"Fetching batch {batch_count} (offset: {offset})...")
                
                response = await client.get(url, timeout=30.0)
                response.raise_for_status()
                
                batch = response.json()
                
                if not batch:
                    print("No more data to fetch.")
                    break
                
                all_markets.extend(batch)
                print(f"Fetched {len(batch)} markets in this batch")
                print(f"Total markets so far: {len(all_markets)}")
                
                # If we got fewer results than the limit, we've reached the end
                if len(batch) < limit:
                    print("Reached end of data (batch smaller than limit)")
                    break
                
                offset += limit
                
                # Add a small delay to be respectful to the API
                await asyncio.sleep(0.1)
            
            print(f"\n=== FETCH COMPLETE ===")
            print(f"Total markets fetched: {len(all_markets)}")
            
            # Prepare the response data
            response_data = {
                "timestamp": datetime.now().isoformat(),
                "total_markets": len(all_markets),
                "api_url": BASE_API_URL,
                "markets": all_markets
            }
            
            # Save to response.json
            with open("response.json", "w", encoding="utf-8") as f:
                json.dump(response_data, f, indent=2, ensure_ascii=False)
            
            print(f"Response saved to response.json")
            print(f"File size: {len(json.dumps(response_data))} characters")
            
            # Print some sample data
            if all_markets:
                print(f"\n=== SAMPLE MARKET DATA ===")
                sample_market = all_markets[0]
                print(f"First market ID: {sample_market.get('id', 'N/A')}")
                print(f"First market question: {sample_market.get('question', 'N/A')}")
                print(f"First market price: {sample_market.get('lastTradePrice', 'N/A')}")
                print(f"First market volume: {sample_market.get('volume', 'N/A')}")
            
        except httpx.HTTPStatusError as e:
            print(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
            return False
        except httpx.RequestError as e:
            print(f"Request error occurred: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error occurred: {e}")
            return False
    
    return True

def main():
    """Main function to run the script"""
    print("="*60)
    print("POLYMARKET API RESPONSE FETCHER")
    print("="*60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run the async function
    success = asyncio.run(fetch_and_save_response())
    
    if success:
        print(f"\n✅ SUCCESS: API response saved to response.json")
    else:
        print(f"\n❌ FAILED: Could not fetch API response")
    
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)

if __name__ == "__main__":
    main()
