import asyncio
from tasks import fetch_markets  # your existing fetch_markets()
from mongo_manager import markets_collection

async def check_already_processed():
    """
    For each market returned by your fetcher, determine if it's already
    in the DB, and keep counts of NEW vs PROCESSED.
    """
    markets = await fetch_markets()
    total = len(markets)
    new_count = 0
    processed_count = 0

    for m in markets:
        q = m["question"]
        # Same lookup your refresh uses:
        doc = await markets_collection.find_one({"question": q})
        if doc:
            processed_count += 1
        else:
            new_count += 1

    print(f"\nOut of {total} markets fetched:")
    print(f"  ✅ PROCESSED (already in DB): {processed_count}")
    print(f"  🆕 NEW (not yet in DB):      {new_count}\n")

if __name__ == "__main__":
    asyncio.run(check_already_processed())
