import asyncio
import logging
import time
from datetime import datetime, timezone
from mongo_manager import markets_collection, classification_queue, validate_connection
from tasks import classify_news_story

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global worker state
is_worker_running = False
worker_lock = asyncio.Lock()

async def delete_queue_item(item, market_slug, reason):
    """
    Delete an item from the classification queue with retry logic.
    
    Args:
        item: The queue item to delete
        market_slug: The market slug for logging
        reason: Reason for deletion for logging
        
    Returns:
        bool: True if deletion was successful, False otherwise
    """
    max_delete_attempts = 3
    delete_success = False
    
    for delete_attempt in range(1, max_delete_attempts + 1):
        try:
            result = await classification_queue.delete_one({"_id": item["_id"]})
            if result.deleted_count > 0:
                logger.info(f"Queue item deleted: {market_slug} (reason: {reason}, attempt: {delete_attempt})")
                delete_success = True
                break
            else:
                logger.warning(f"Failed to delete queue item for {market_slug} (attempt {delete_attempt}/{max_delete_attempts})")
                
                # Double-check if item still exists
                still_exists = await classification_queue.find_one({"_id": item["_id"]})
                if not still_exists:
                    logger.info(f"Queue item {market_slug} no longer exists in queue, considering delete successful")
                    delete_success = True
                    break
                elif delete_attempt < max_delete_attempts:
                    logger.warning(f"Will retry deletion for {market_slug}")
                    await asyncio.sleep(0.5)
        except Exception as e:
            logger.error(f"Error deleting queue item {market_slug}: {str(e)} (attempt {delete_attempt}/{max_delete_attempts})")
            if delete_attempt < max_delete_attempts:
                await asyncio.sleep(0.5)
    
    if delete_success:
        logger.info(f"QUEUE ITEM DELETED: Market {market_slug} removed from queue")
    else:
        logger.error(f"Failed to delete queue item after {max_delete_attempts} attempts for {market_slug}")
    
    return delete_success

async def update_market_classification(market_slug, question, new_classification):
    """
    Update a market's classification in the database, using direct question matching.
    
    Args:
        market_slug: The market identifier (not used for primary update)
        question: The market question text
        new_classification: The classification to set
        
    Returns:
        bool: True if update succeeded, False if failed
    """
    print(f"Attempting to update classification to '{new_classification}'")
    print(f"Question: '{question[:50]}...'")
    
    # First check if the market exists with this question
    market_by_question = await markets_collection.find_one({"question": question})
    
    if market_by_question:
        found_slug = market_by_question.get("market_slug")
        print(f"Found market with matching question, ID: {found_slug}")
        
        # Update directly using the question (simplest, most direct approach)
        update_result = await markets_collection.update_one(
            {"question": question},
            {"$set": {"classification": new_classification}}
        )
        
        if update_result.modified_count > 0:
            print(f"Successfully updated market by question match → '{new_classification}'")
            return True
        else:
            print(f"Failed to update market despite finding it by question")
            print(f"Modified count: {update_result.modified_count}, Matched count: {update_result.matched_count}")
            
            # Check if classification is already set to the same value
            if market_by_question.get("classification") == new_classification:
                print(f"Market already has classification '{new_classification}', no update needed")
                return True
    else:
        # No match by question, try the market_slug as fallback
        print(f"No market found with exact question match, trying market_slug: {market_slug}")
        market_by_slug = await markets_collection.find_one({"market_slug": market_slug})
        
        if market_by_slug:
            update_by_slug = await markets_collection.update_one(
                {"market_slug": market_slug},
                {"$set": {"classification": new_classification}}
            )
            
            if update_by_slug.modified_count > 0:
                print(f"Successfully updated using market_slug: {market_slug}")
                return True
            else:
                print(f"Failed to update by market_slug despite finding the market")
                print(f"Current classification: '{market_by_slug.get('classification')}'")
                
                # Check if classification is already set to the same value
                if market_by_slug.get("classification") == new_classification:
                    print(f"Market already has classification '{new_classification}', no update needed")
                    return True
        else:
            print(f"No market found with market_slug '{market_slug}' either")
    
    # If we get here, all update attempts failed
    print(f"ERROR: Failed to update classification for question: '{question[:50]}...'")
    return False

async def process_queue_item(item):
    """
    Process a single item from the classification queue.
    
    Args:
        item: The queue item to process
        
    Returns:
        tuple: (success, error) indicating whether processing succeeded or had an error
    """
    if not item:
        logger.error("Received None or empty item to process")
        return False, True
        
    try:
        # Validate item has required fields
        if "market_slug" not in item or "question" not in item:
            logger.error(f"Queue item is missing required fields: {item}")
            await classification_queue.delete_one({"_id": item["_id"]})
            return True, False  # Mark as success to remove from queue
            
        market_slug = item["market_slug"]
        question = item["question"]
        
        if not market_slug or not question:
            logger.error(f"Queue item has empty market_slug or question: {item}")
            await classification_queue.delete_one({"_id": item["_id"]})
            return True, False  # Mark as success to remove from queue
            
        question_preview = question[:150] + "..." if len(question) > 150 else question
        
        logger.info(f"\nProcessing: {market_slug}")
        logger.info(f"Question: '{question_preview}'")
        
        # Step 1: Verify market exists
        market = await markets_collection.find_one({"market_slug": market_slug})
        if not market:
            logger.warning(f"Market {market_slug} no longer exists, removing from queue")
            await delete_queue_item(item, market_slug, "market no longer exists")
            return True, False
        
        # Step 2: Check current classification
        current_classification = market.get("classification", "N/A")
        logger.info(f"Market {market_slug} current classification: '{current_classification}'")
        
        # Skip if already classified with a valid category
        market_by_question = await markets_collection.find_one({"question": question})
        if market_by_question and market_by_question.get("classification") != "N/A":
            logger.info(f"Market {market_slug} already classified as {current_classification}, removing from queue(Class_worker.py)")
            await delete_queue_item(item, market_slug, f"already classified as {current_classification}")
            return True, False
        
        # Also check if current_classification is not N/A (double-check)
        if current_classification != "N/A":
            logger.info(f"Market {market_slug} has non-N/A classification '{current_classification}', removing from queue")
            await delete_queue_item(item, market_slug, f"already has classification '{current_classification}'")
            return True, False
            
        # Step 3: Perform classification
        logger.info(f"Going to Classify the market: {market_slug} (previously classified as: '{current_classification}')")
        
        try:
            classifier_response = await classify_news_story(question)
            logger.info(f"Raw API response: {classifier_response}")
        except Exception as api_error:
            logger.error(f"Classification API call failed: {api_error}")
            await classification_queue.update_one(
                {"_id": item["_id"]},
                {
                    "$set": {
                        "last_error": f"API call failed: {str(api_error)}",
                        "last_attempt": datetime.now(timezone.utc)
                    },
                    "$inc": {"attempts": 1}
                }
            )
            return False, True
        
        # Handle API errors
        if isinstance(classifier_response, dict) and "error" in classifier_response:
            await classification_queue.update_one(
                {"_id": item["_id"]},
                {
                    "$set": {
                        "last_error": classifier_response.get("error", "Unknown error"),
                        "last_attempt": datetime.now(timezone.utc)
                    },
                    "$inc": {"attempts": 1}
                }
            )
            logger.error(f"Classification API error for {market_slug}: {classifier_response.get('error')}")
            return False, True
        
        # Step 4: Extract classification and apply logic
        if not isinstance(classifier_response, dict) or "answer" not in classifier_response:
            logger.error(f"Invalid response format from classifier: {classifier_response}")
            await classification_queue.update_one(
                {"_id": item["_id"]},
                {
                    "$set": {
                        "last_error": "Invalid response format from classifier",
                        "last_attempt": datetime.now(timezone.utc)
                    },
                    "$inc": {"attempts": 1}
                }
            )
            return False, True
            
        new_classification = classifier_response.get("answer", "N/A")
        
        logger.info(f"Class_worker.py-->Market classified as: '{new_classification}' (was: '{current_classification}')")
        logger.info(f"Class_worker.py-->CLASSIFICATION RESULT: '{question_preview}' => {new_classification}")
        
        # Step 5: Update the market with new classification
        try:
            update_success = await update_market_classification(market_slug, question, new_classification)
        except Exception as update_error:
            logger.error(f"Error updating classification: {update_error}")
            await classification_queue.update_one(
                {"_id": item["_id"]},
                {
                    "$set": {
                        "last_error": f"Update error: {str(update_error)}",
                        "last_attempt": datetime.now(timezone.utc)
                    },
                    "$inc": {"attempts": 1}
                }
            )
            return False, True
        
        # Step 6: Remove from queue if successfully classified
        if update_success:
            try:
                delete_success = await delete_queue_item(
                    item, market_slug, f"successfully classified as '{new_classification}'"
                )
                return True, False
            except Exception as delete_error:
                logger.error(f"Error deleting queue item: {delete_error}")
                return False, True
        else:
            logger.error(f"Failed to update classification for {market_slug}")
            await classification_queue.update_one(
                {"_id": item["_id"]},
                {
                    "$set": {
                        "last_error": "Failed to update classification in database",
                        "last_attempt": datetime.now(timezone.utc)
                    },
                    "$inc": {"attempts": 1}
                }
            )
            return False, True
            
    except Exception as e:
        market_slug = item.get("market_slug", "unknown")
        logger.exception(f"Error processing {market_slug}: {str(e)}")
        # Update attempt count and error info
        try:
            await classification_queue.update_one(
                {"_id": item["_id"]},
                {
                    "$set": {
                        "last_error": str(e),
                        "last_attempt": datetime.now(timezone.utc)
                    },
                    "$inc": {"attempts": 1}
                }
            )
        except Exception as update_error:
            logger.error(f"Error updating queue item error info: {update_error}")
            
        return False, True

async def process_classification_queue(batch_size=10, delay_seconds=1):
    """
    Process the classification queue in batches with controlled throughput.
    Runs independently from the main refresh cycle.
    
    Args:
        batch_size: Number of items to process in each batch
        delay_seconds: Base delay between batches to prevent API rate limiting
    """
    global is_worker_running
    
    async with worker_lock:
        if is_worker_running:
            return
        is_worker_running = True
    
    try:
        # Validate database connection first
        connection_valid = await validate_connection()
        if not connection_valid:
            logger.error("ERROR: MongoDB connection invalid and reconnection failed. Aborting worker.")
            async with worker_lock:
                is_worker_running = False
            return
            
        # Print worker start header and initial queue size
        logger.info("\n\n========== CLASSIFICATION WORKER STARTED ==========")
        logger.info(f"Time: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')}")
        total_queue_size = await classification_queue.count_documents({})
        logger.info(f"Total items in queue are: {total_queue_size}")
        
        processed_count = 0
        error_count = 0
        
        # Process in batches until queue is empty
        while True:
            # Get a batch of items, prioritizing those with fewer attempts
            items = await classification_queue.find().sort("attempts", 1).limit(batch_size).to_list(batch_size)
            
            if not items:
                logger.info(f"\nQueue empty, worker stopping.")
                logger.info(f"FINAL RESULTS: Processed: {processed_count}, Errors: {error_count}")
                logger.info("=================================================\n")
                break
                
            # Get current queue size before processing this batch
            current_queue_size = await classification_queue.count_documents({})
            
            # Print batch processing header
            batch_start = time.time()
            logger.info(f"\n----- PROCESSING BATCH OF {len(items)} ITEMS -----")
            logger.info(f"TOTAL ITEMS IN QUEUE BEFORE BATCH: {current_queue_size}")
            logger.info(f"Batch size: {len(items)} items")
            logger.info(f"Time: {datetime.now(timezone.utc).strftime('%H:%M:%S')}")
            
            # Process each item in the batch
            for item in items:
                success, error = await process_queue_item(item)
                if success:
                    processed_count += 1
                if error:
                    error_count += 1
            
            # Get detailed queue statistics after processing the batch
            batch_time = time.time() - batch_start
            remaining_queue = await classification_queue.count_documents({})
            queue_new_items = await classification_queue.count_documents({"attempts": 0})
            queue_retry_items = await classification_queue.count_documents({"attempts": {"$gt": 0}})
            
            # Print batch summary
            logger.info(f"\n----- BATCH SUMMARY -----")
            logger.info(f"Batch processed in {batch_time:.2f}s")
            logger.info(f"Items in this batch: {len(items)}")
            logger.info(f"Successfully processed: {processed_count} items")
            logger.info(f"Errors: {error_count}")
            logger.info(f"TOTAL ITEMS IN QUEUE ARE: {remaining_queue}")
            logger.info(f"Queue breakdown: {queue_new_items} new, {queue_retry_items} retries")

            # Add database status information
            total_markets = await markets_collection.count_documents({})
            classified_markets = await markets_collection.count_documents({"classification": {"$nin": ["N/A"]}})
            unclassified_markets = await markets_collection.count_documents({"classification": "N/A"})

            logger.info(f"\n----- DATABASE STATUS -----")
            logger.info(f"Total markets in database: {total_markets}")
            logger.info(f"Classified markets: {classified_markets} ({(classified_markets/total_markets*100):.1f}%)")
            logger.info(f"Unclassified markets (N/A): {unclassified_markets} ({(unclassified_markets/total_markets*100):.1f}%)")
            logger.info("-------------------------")
            
            logger.info(f"\n===> QUEUE SIZE: {remaining_queue} items remaining <===\n")
            
            # Adaptive delay - process faster if queue is large
            adaptive_delay = max(0.5, delay_seconds - (0.05 * min(remaining_queue // 10, 5)))
            
            # Sleep between batches to avoid API rate limits
            await asyncio.sleep(adaptive_delay)
    
    finally:
        # Reset worker running flag when finished
        async with worker_lock:
            is_worker_running = False
            logger.info("\n========== WORKER STOPPED ==========\n")

async def ensure_worker_running():
    """
    Start the classification worker if there are items in the queue and worker isn't running.
    
    Returns:
        bool: True if a worker was started, False otherwise
    """
    global is_worker_running
    
    if not is_worker_running:
        # Validate connection before checking queue size
        connection_valid = await validate_connection()
        if not connection_valid:
            logger.error("ERROR: MongoDB connection invalid and reconnection failed.")
            return False
            
        queue_size = await classification_queue.count_documents({})
        print("Your script is in ensure_worker_running function.\n")
        print("============================================\n")
        print(f"Queue size in ensure_worker_running function: {queue_size}")
        print("============================================\n")
        if queue_size > 0:
            # Start worker as background task
            asyncio.create_task(process_classification_queue())
            return True
    return False
