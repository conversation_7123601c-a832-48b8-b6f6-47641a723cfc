
import httpx
import asyncio
import pandas as pd
from datetime import datetime, timezone
from statistics import stdev
from openai import AsyncOpenAI
from mongo_manager import markets_collection, classification_queue, validate_connection, repair_database, restore_from_backup, create_database_backup
import motor 
import random
import math
import json
import re
import os
import time

Widget_data = []  # Global list to store fully processed market entries


BASE_API_URL = "https://gamma-api.polymarket.com/events"
OPENAI_CLIENT = AsyncOpenAI(api_key="sk-ef21b3eeee2041b0bf0f061f8d45b312")

# Create a global asyncio lock to avoid overlapping refreshes
refresh_lock = asyncio.Lock()

# Global variables to control execution flow
refresh_in_progress = False

# Track previous database size to detect sudden drops
last_known_db_size = None
DB_SIZE_DROP_THRESHOLD = 0.2  # 20% drop is considered significant
MINOR_SIZE_REDUCTION_THRESHOLD = 0.05  # 5% drop is considered minor change worth backing up

# File to store last known DB size between runs
DB_SIZE_TRACKER_FILE = os.path.join(os.path.dirname(__file__), "last_db_size.txt")
# Lock file for DB size tracker
DB_SIZE_LOCK_FILE = os.path.join(os.path.dirname(__file__), "last_db_size.lock")

# Global lock for file operations
file_operation_lock = asyncio.Lock()

def format_number(num):
    """Format number to keep only the decimal part and remove trailing zeros."""
    try:
        if num is None or num == 0:
            return "0"

        # Convert to float if it's not already
        num = float(num)

        # Format to 3 decimal places
        formatted = f"{num:.3f}"

        # Find the decimal point
        if "." in formatted:
            decimal_part = formatted[formatted.index("."):]
            # Remove trailing zeros but keep at least one decimal place
            decimal_part = decimal_part.rstrip('0')
            if decimal_part == ".":
                decimal_part = ".0"
            return decimal_part
        else:
            return ".0"
    except (ValueError, TypeError):
        return ".0"

def save_db_size(size):
    """
    Save current database size to file for persistence between runs
    Uses a simple lock file approach that works cross-platform
    """
    try:
        # Create a unique lock file name
        lock_file = DB_SIZE_LOCK_FILE
        retry_count = 0
        max_retries = 5
        
        while retry_count < max_retries:
            # Check if lock file exists
            if os.path.exists(lock_file):
                # Check if lock is stale (older than 30 seconds)
                if time.time() - os.path.getmtime(lock_file) > 30:
                    try:
                        os.remove(lock_file)
                        print("Removed stale lock file")
                    except:
                        pass
                else:
                    # Lock exists and is fresh, wait and retry
                    print(f"Lock file exists, waiting... (attempt {retry_count+1}/{max_retries})")
                    time.sleep(1)
                    retry_count += 1
                    continue
            
            try:
                # Create lock file
                with open(lock_file, 'w') as f:
                    f.write(f"Locked at {datetime.now().isoformat()}")
                
                # Now we have the lock, write the data
                with open(DB_SIZE_TRACKER_FILE, 'w') as f:
                    f.write(str(size))
                
                # Release lock
                os.remove(lock_file)
                print(f"Saved database size ({size}) to tracking file")
                return
            except Exception as e:
                print(f"Error during file operation: {e}")
                try:
                    # Always try to remove lock on error
                    if os.path.exists(lock_file):
                        os.remove(lock_file)
                except:
                    pass
                retry_count += 1
        
        print(f"Failed to save database size after {max_retries} attempts")
    except Exception as e:
        print(f"Failed to save database size: {e}")

def load_last_db_size():
    """
    Load last known database size from file
    Uses a cross-platform file locking approach
    """
    try:
        if not os.path.exists(DB_SIZE_TRACKER_FILE):
            return None
            
        lock_file = DB_SIZE_LOCK_FILE
        retry_count = 0
        max_retries = 5
        
        while retry_count < max_retries:
            # Check if lock file exists
            if os.path.exists(lock_file):
                # Check if lock is stale (older than 30 seconds)
                if time.time() - os.path.getmtime(lock_file) > 30:
                    try:
                        os.remove(lock_file)
                        print("Removed stale lock file")
                    except:
                        pass
                else:
                    # Lock exists and is fresh, wait and retry
                    print(f"Lock file exists, waiting to read... (attempt {retry_count+1}/{max_retries})")
                    time.sleep(1)
                    retry_count += 1
                    continue
            
            try:
                # We don't need to create a lock file for reading,
                # just check the lock file doesn't exist (meaning no one is writing)
                with open(DB_SIZE_TRACKER_FILE, 'r') as f:
                    content = f.read().strip()
                    if content:
                        size = int(content)
                        print(f"Loaded database size ({size}) from tracking file")
                        return size
                return None
            except Exception as e:
                print(f"Error reading size file: {e}")
                retry_count += 1
        
        print(f"Failed to read database size after {max_retries} attempts")
        return None
    except Exception as e:
        print(f"Failed to load last database size: {e}")
    return None

# Async wrapper for file operations 
async def async_save_db_size(size):
    """Async wrapper for save_db_size to use in async context"""
    # Use an asyncio lock to prevent multiple coroutines from running this at once
    async with file_operation_lock:
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, save_db_size, size)

async def async_load_db_size():
    """Async wrapper for load_last_db_size to use in async context"""
    # Use an asyncio lock to prevent multiple coroutines from running this at once
    async with file_operation_lock:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, load_last_db_size)

def calculate_odds_trend(prices):
    """Calculate odds trend with improved error handling and validation."""
    try:
        # Validate input
        if not prices or len(prices) < 2:
            return "N/A"

        # Filter out invalid prices (None, 0, negative values)
        valid_prices = []
        for price in prices:
            try:
                p = float(price)
                if p > 0:  # Only include positive prices
                    valid_prices.append(p)
            except (ValueError, TypeError):
                continue

        # Need at least 2 valid prices for trend calculation
        if len(valid_prices) < 2:
            return "N/A"

        # If we have exactly 2 prices, just compare them
        if len(valid_prices) == 2:
            start_price = round(valid_prices[0], 3)
            end_price = round(valid_prices[1], 3)

            if start_price > end_price:
                return f"{format_number(start_price)} 📉 {format_number(end_price)}"
            elif start_price < end_price:
                return f"{format_number(start_price)} 📈 {format_number(end_price)}"
            else:
                return f"{format_number(start_price)} ➡️ {format_number(end_price)}"

        # For more than 2 prices, use the original logic
        mid_index = len(valid_prices) // 2
        first_half = valid_prices[:mid_index]
        second_half = valid_prices[mid_index:]

        # Ensure both halves have at least one element
        if len(first_half) == 0 or len(second_half) == 0:
            # Fallback to first vs last comparison
            start_price = round(valid_prices[0], 3)
            end_price = round(valid_prices[-1], 3)

            if start_price > end_price:
                return f"{format_number(start_price)} 📉 {format_number(end_price)}"
            elif start_price < end_price:
                return f"{format_number(start_price)} 📈 {format_number(end_price)}"
            else:
                return f"{format_number(start_price)} ➡️ {format_number(end_price)}"

        avg_first_half = round(sum(first_half) / len(first_half), 3)
        avg_second_half = round(sum(second_half) / len(second_half), 3)

        # If the averages are the same, compare the very first vs. last price
        if avg_first_half == avg_second_half:
            start_price = round(valid_prices[0], 3)
            end_price = round(valid_prices[-1], 3)

            if start_price > end_price:
                return f"{format_number(start_price)} 📉 {format_number(end_price)}"
            elif start_price < end_price:
                return f"{format_number(start_price)} 📈 {format_number(end_price)}"
            return f"{format_number(start_price)} ➡️ {format_number(end_price)}"

        # If average is ascending
        if avg_first_half < avg_second_half:
            min_first = min(first_half)
            max_second = max(second_half)
            return f"{format_number(min_first)} 📈 {format_number(max_second)}"
        else:
            # Descending
            max_first = max(first_half)
            min_second = min(second_half)
            return f"{format_number(max_first)} 📉 {format_number(min_second)}"

    except Exception as e:
        print(f"Error calculating odds trend: {e}")
        return "N/A"


async def fetch_markets():
    """
    Fetch all markets from Polymarket API and return them as a list.
    """
    async with httpx.AsyncClient() as client:
        markets = []
        offset = 0
        limit = 500

        while True:
            url = f"{BASE_API_URL}?limit={limit}&offset={offset}&closed=false"
            response = await client.get(url, timeout=10.0)
            response.raise_for_status()
            batch = response.json()
            
            if not batch:
                break

            markets.extend(batch)
            if len(batch) < limit:
                break
            
            offset += limit

        new_markets = []
        for comp in markets:
            try:
                for mkt in comp["markets"]:
                    if mkt["closed"]:
                        continue
                    try:
                        # Keep only markets with lastTradePrice between 0 and 1
                        if 0 < mkt["lastTradePrice"] < 1:
                            mkt["slg"] = comp["slug"]
                            new_markets.append(mkt)
                        else:
                            continue
                    except Exception:
                        pass
            except Exception as e:
                print(comp)
                print(e)

        return new_markets

async def classify_news_story(story):
    """
    Classifies a news story into categories using the DeepSeek API.
    """
    categories = ["Politics", "Crypto", "Business", "Culture", "Technology", "Sports", "Tweets", "Weather", "Other"]
    prompt = f"Read the following news story and assign one of these categories based on the content: {', '.join(categories)}\n\nStory: {story} return your response in json like this " + "{'answer': 'category'}"

    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.post(
                "https://api.deepseek.com/chat/completions",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer sk-ef21b3eeee2041b0bf0f061f8d45b312"
                },
                json={
                    "model": "deepseek-chat",
                    "messages": [
                        {"role": "system", "content": "You are a knowledgeable assistant helping to classify news stories into categories."},
                        {"role": "user", "content": prompt}
                    ],
                    "response_format": {"type": "json_object"}
                }
            )

            response.raise_for_status()
            result = response.json()

            try:
                content = result["choices"][0]["message"]["content"]
                print(f"API Response: {content[:200]}")
                return json.loads(content)
            except (KeyError, json.JSONDecodeError) as e:
                print(f"Error parsing API response: {e}")
                print(f"Raw response: {result}")
                # Use "Other" instead of "Pending"
                return {"answer": "Other", "error": f"Failed to parse response: {e}"}

        except httpx.ReadTimeout:
            print("ERROR: Request timed out")
            # Use "Other" instead of "Pending"
            return {"answer": "Other", "error": "Request timed out"}
        
        except httpx.HTTPStatusError as e:
            status_code = e.response.status_code
            error_text = e.response.text
            
            # Check for "Content Exists Risk" in the error text
            if status_code == 400 and "Content Exists Risk" in error_text:
                print(f"NOTICE: Content policy violation detected in: {story[:50]}...")
                # Use "Other" for content policy violations
                return {"answer": "Other"}
            
            print(f"ERROR: HTTP error occurred: {status_code} - {error_text}")
            # Use "Other" for all HTTP errors
            return {"answer": "Other", "error": f"HTTP error {status_code}"}
        
        except Exception as e:
            print(f"Unexpected Error: {e}")
            # Use "Other" for all unexpected errors
            return {"answer": "Other", "error": "Unexpected error"}


##########################################################################################



async def deep_seek_analysis_ai(question: str):
    """
    Complete market impact analysis with DeepSeek AI
    Returns:
        {
            "moneybag": bool (market impact yes/no),
            "ox": list (bullish tickers),
            "bear": list (bearish tickers)
        }
    """
    base_url = "https://api.deepseek.com/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer sk-ef21b3eeee2041b0bf0f061f8d45b312"  # Replace with your actual API key
    }

    try:
        async with httpx.AsyncClient(timeout=30) as client:
            # ===== STEP 1: MARKET IMPACT CHECK =====
            print("\n🔍 Step 1: Checking market impact...")
            impact_query = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system", 
                        "content": "You're a financial analyst. Respond with JSON only."
                    },
                    {
                        "role": "user",
                        "content": (
                            f"Does this event likely impact financial markets? "
                            f"Respond with JSON format: {{\"market_impact\": \"yes\" or \"no\"}}\n\n"
                            f"Event: {question}"
                        )
                    }
                ],
                "response_format": {"type": "json_object"}
            }
            
            res1 = await client.post(base_url, headers=headers, json=impact_query)
            res1.raise_for_status()
            res1_json = res1.json()
            impact_response = json.loads(res1_json["choices"][0]["message"]["content"])
            
            print(f"Impact Response: {impact_response}")
            
            if impact_response.get("market_impact", "no").lower() != "yes":
                print("➡️ No significant market impact detected")
                return {
                    "moneybag": False,
                    "ox": [],
                    "bear": []
                }

            # ===== STEP 2: BULLISH ASSETS =====
            print("\n📈 Step 2: Identifying bullish assets...")
            bullish_query = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "Identify assets likely to rise. Return JSON with tickers only."
                    },
                    {
                        "role": "user",
                        "content": (
                            f"What 3 assets (stocks, commodities, cryptocurrencies) would benefit most? "
                            f"Return as: {{\"bullish\": [\"TICKER1\", \"TICKER2\"]}}\n\n"
                            f"Event: {question}"
                        )
                    }
                ],
                "response_format": {"type": "json_object"},
                "temperature": 0.7  # Slightly creative for better ticker suggestions
            }
            
            res2 = await client.post(base_url, headers=headers, json=bullish_query)
            res2.raise_for_status()
            bull_json = res2.json()
            bull_data = json.loads(bull_json["choices"][0]["message"]["content"])
            bull_list = [x.upper().strip() for x in bull_data.get("bullish", []) if len(x) <= 6][:3]
            
            print(f"Bullish Assets: {bull_list}")

            # ===== STEP 3: BEARISH ASSETS =====
            print("\n📉 Step 3: Identifying bearish assets...")
            bearish_query = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "Identify assets likely to fall. Return JSON with tickers only."
                    },
                    {
                        "role": "user",
                        "content": (
                            f"What 3 assets would be hurt most by this event? "
                            f"Return as: {{\"bearish\": [\"TICKER1\", \"TICKER2\"]}}\n\n"
                            f"Event: {question}"
                        )
                    }
                ],
                "response_format": {"type": "json_object"}
            }
            
            res3 = await client.post(base_url, headers=headers, json=bearish_query)
            res3.raise_for_status()
            bear_json = res3.json()
            bear_data = json.loads(bear_json["choices"][0]["message"]["content"])
            bear_list = [x.upper().strip() for x in bear_data.get("bearish", []) if len(x) <= 6][:3]
            
            print(f"Bearish Assets: {bear_list}")

            return {
                "moneybag": True,
                "ox": bull_list,
                "bear": bear_list
            }

    except httpx.HTTPStatusError as e:
        print(f"HTTP Error: {e.response.status_code} - {e.response.text}")
    except json.JSONDecodeError as e:
        print(f"JSON Parsing Error: {e}")
    except Exception as e:
        print(f"Unexpected Error: {str(e)}")
    
    return {
        "moneybag": False,
        "ox": [],
        "bear": []
    }

#######################################################################################################
async def refresh_markets():
    """
    Fetch and process active markets with strict sequential flow.
    """
    global refresh_in_progress, last_known_db_size

    # Prevent multiple refresh processes
    if refresh_in_progress:
        print("Refresh already in progress. Skipping.")
        return

    try:
        refresh_in_progress = True
        import traceback
        from classification_worker import ensure_worker_running

        # First, validate database connection before proceeding
        connection_valid = await validate_connection()
        if not connection_valid:
            print("ERROR: MongoDB connection invalid and reconnection failed. Aborting refresh.")
            refresh_in_progress = False
            return

        print("\n\n" + "="*80)
        print("                     NEW DATA REFRESH CYCLE                     ")
        print("="*80)
        current_time = datetime.now(timezone.utc)
        print(f"Time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # DIAGNOSTIC: Check markets with N/A classification
        na_markets = await markets_collection.find({"classification": "N/A"}).to_list(None)
        print(f"\nDIAGNOSTIC: Found {len(na_markets)} markets with N/A classification")
        
        # Check which of these are in the queue
        for market in na_markets:
            market_slug = market.get("market_slug")
            in_queue = await classification_queue.find_one({"market_slug": market_slug})
            if not in_queue:
                print(f"WARNING: Market {market_slug} has N/A classification but is not in queue")
                # Add to queue if not already there
                await classification_queue.insert_one({
                    "market_slug": market_slug,
                    "question": market.get("question", ""),
                    "queued_at": current_time,
                    "attempts": 0
                })
                print(f"Added market {market_slug} to classification queue")

        # STEP 0: DATABASE HEALTH CHECK
        print("\n--- STEP 0: DATABASE HEALTH CHECK ---")
        # Check database size first, to prevent overwriting if DB got wiped
        db_size = await markets_collection.count_documents({})
        print(f"Current database size: {db_size} markets")

        # Load the last known DB size from persistent storage
        if last_known_db_size is None:
            last_known_db_size = await async_load_db_size()
            print(f"Loaded last known database size: {last_known_db_size}")

        # Initialize a flag for determining if we need to back up after refreshing
        should_backup_after_refresh = False

        # Initialize size reduction tracking
        size_reduction_ratio = 0

        # Detect database size changes
        if last_known_db_size is not None and last_known_db_size > 100:
            # Calculate percentage change
            size_reduction_ratio = 1 - (db_size / last_known_db_size)

            # Restore whenever we detect a 5% or greater data loss
            if size_reduction_ratio >= MINOR_SIZE_REDUCTION_THRESHOLD:
                # DATA LOSS DETECTED (5% or more reduction)
                print(f"DATA LOSS DETECTED: {size_reduction_ratio * 100:.1f}% of database has been lost!")
                print(f"Previous DB size: {last_known_db_size}, Current size: {db_size}")
                print(f"Lost approximately {last_known_db_size - db_size} markets")

                # Directly restore from the most recent backup - no need to backup current reduced state
                print("ATTEMPTING AUTOMATIC DATABASE RESTORATION FROM MOST RECENT BACKUP...")
                restore_success = await restore_from_backup()

                if restore_success:
                    # Check if restore worked
                    restored_db_size = await markets_collection.count_documents({})
                    print(f"Restoration complete. Database size after restore: {restored_db_size} markets")

                    if restored_db_size > db_size:
                        print(f"SUCCESSFUL RESTORATION: Recovered {restored_db_size - db_size} markets")
                        db_size = restored_db_size  # Update current size
                    else:
                        print("RESTORATION WARNING: Restore completed but database size didn't increase significantly")
                else:
                    print("RESTORATION FAILED: Unable to restore from backup")

        # CRITICAL SAFETY CHECK: If the database should have data but appears empty 
        elif db_size < 10:  # Arbitrary small number
            print("WARNING: Database is nearly empty. Running safety checks...")

            # Attempt repair
            print("Attempting database repair...")
            repair_success = await repair_database()
            if repair_success:
                print("Database repair completed.")

            # Check again after repair
            db_size_after_repair = await markets_collection.count_documents({})
            print(f"Database size after repair: {db_size_after_repair} markets")

            # If still small but we have backups, try to restore
            if db_size_after_repair < 10:
                print("DATABASE SIZE IS CRITICALLY LOW. This might indicate data loss.")
                print("Attempting to restore from most recent backup...")

                restore_success = await restore_from_backup()
                if restore_success:
                    restored_db_size = await markets_collection.count_documents({})
                    print(f"Restoration complete. Database size after restore: {restored_db_size} markets")
                    db_size = restored_db_size  # Update current size
                else:
                    print("WARNING: Unable to restore from backup. Will continue with current data.")
                    print("This might be a fresh start or all backups might be corrupted.")

        # Create a backup at the start of each refresh cycle if we haven't already for emergency reasons
        if size_reduction_ratio < DB_SIZE_DROP_THRESHOLD:  # Skip if we already did an emergency backup
            print("Creating routine backup before processing new data...")
            backup_file = await create_database_backup()
            if backup_file:
                print(f"Created backup at: {backup_file}")

        # Update the last known DB size for future checks 
        if db_size > 10:  # Only track meaningful DB sizes
            last_known_db_size = db_size
            await async_save_db_size(db_size)
            print(f"Updated last known database size to: {db_size}")

        # STEP 1: FETCH MARKETS - Make sure this completes before anything else
        print("\n--- STEP 1: FETCHING MARKETS ---")
        active_markets = await fetch_markets()
        print(f"Fetched {len(active_markets)} markets from API")

        if not active_markets:
            print("No markets found. Ending refresh cycle.")
            refresh_in_progress = False
            return

        # STEP 2: GET QUEUE STATUS - before processing
        print("\n--- STEP 2: CHECKING QUEUE STATUS ---")
        previous_queue_size = await classification_queue.count_documents({})
        print(f"Current queue size before processing: {previous_queue_size}")

        # STEP 3: PROCESS MARKETS
        print("\n--- STEP 3: PROCESSING MARKETS ---")
        markets_needing_classification = []  # Collect markets needing classification
        markets_processed = 0

        global Widget_data
        Widget_data = []  # Clear old data at start of refresh

        # Process each market sequentially
        for market in active_markets:
            try:
                market_id = market.get("id", "unknown")
                print(f"\nProcessing market {market_id} ({markets_processed+1}/{len(active_markets)})")

                market_slug = str(market_id)
                market_question = market.get("question", "")

                # Find existing data by market_slug only
                past_data = await markets_collection.find_one({"market_slug": market_slug})

                # Get stored values
                old_prices = past_data.get("prices", []) if past_data else []
                old_classification = past_data.get("classification", "N/A") if past_data else "N/A"

                # Calculate current price and volatility with better error handling
                try:
                    current_price = float(market.get("lastTradePrice", 0))
                    # Ensure current price is valid
                    if current_price <= 0:
                        # Try alternative price fields
                        current_price = float(market.get("price", 0))
                        if current_price <= 0:
                            current_price = 0.5  # Default fallback price
                except (ValueError, TypeError):
                    current_price = 0.5  # Default fallback price

                # Build price history with validation
                valid_old_prices = []
                if old_prices:
                    for price in old_prices:
                        try:
                            p = float(price)
                            if p > 0:
                                valid_old_prices.append(p)
                        except (ValueError, TypeError):
                            continue

                all_prices = valid_old_prices + [current_price]
                volatility = stdev(all_prices) if len(all_prices) > 1 else 0

                # Calculate percentage change
                percentage_change = 0
                if len(valid_old_prices) > 0:
                    old_price = valid_old_prices[-1]
                    try:
                        percentage_change = ((current_price - old_price) / old_price) * 100
                    except (ZeroDivisionError, TypeError):
                        percentage_change = 0

                # Calculate trends with improved error handling
                trend = calculate_odds_trend(all_prices)
                imp = volatility * float(market.get("volume", 0)) if market.get("volume") else 0
                
                # Check classification
                if past_data and past_data.get("classification") != "N/A":
                    needs_classification = False
                    new_classification = old_classification
                    print(f"Market {market_slug} already classified as '{old_classification}' - skipping queue")
                else:
                    # Only set needs_classification to True for new markets or those with N/A
                    needs_classification = True
                    new_classification = "N/A"
                    print(f"Market {market_slug} needs classification")

                # Handle impact data - run in background for new markets
                reuse_impact = (
                    past_data and all(k in past_data for k in ["moneybag", "ox", "bear"])
                    and isinstance(past_data.get("ox", []), list)
                    and isinstance(past_data.get("bear", []), list)
                )

                if reuse_impact:
                    moneybag = past_data.get("moneybag", False)
                    ox = past_data.get("ox", [])
                    bear = past_data.get("bear", [])
                else:
                    # For existing markets without impact data, keep their current moneybag status
                    # For truly new markets, set default values
                    if past_data:
                        # Existing market missing impact data - preserve any existing moneybag value
                        moneybag = past_data.get("moneybag", False)
                        ox = past_data.get("ox", [])
                        bear = past_data.get("bear", [])
                        
                        # Only run background analysis if completely missing impact data
                        if not all(k in past_data for k in ["moneybag", "ox", "bear"]):
                            asyncio.create_task(update_market_impact_background(market_slug, market_question))
                            print(f"Scheduled background impact analysis for existing market {market_slug}")
                    else:
                        # Truly new market - set defaults and schedule background analysis
                        moneybag = False
                        ox = []
                        bear = []
                        asyncio.create_task(update_market_impact_background(market_slug, market_question))
                        print(f"Scheduled background impact analysis for new market {market_slug}")

                # Prepare update fields
                update_fields = {
                    "question": market_question,
                    "odds": current_price,
                    "volume": market.get("volume"),
                    "spread": market.get("spread"),
                    "volatility": volatility,
                    "percentage_change": percentage_change,
                    "trend": trend,
                    "imp": imp,
                    "moneybag": moneybag,
                    "ox": ox,
                    "bear": bear,
                    "lastUpdated": current_time,
                    "classification": new_classification
                }

                # Add to Widget_data with classification included
                Widget_data.append({
                    **update_fields,
                    "market_slug": market_slug,
                    "question": market_question
                })

                print(f"Widget_data appended for market {market_question[:50]}... (Classification: {update_fields.get('classification', 'N/A')})")
                
                # Insert or update in database
                try:
                    if past_data is None:
                        print(f"Inserting new market: {market_slug}")
                        await markets_collection.insert_one({
                            "question": market_question,
                            "market_slug": market_slug,
                            **update_fields,
                            "prices": old_prices,
                            "created_at": current_time
                        })
                    else:
                        print(f"Updating market: {market_slug}")
                        await markets_collection.update_one(
                            {"market_slug": market_slug},
                            {
                                "$set": update_fields,
                                "$push": {"prices": {"$each": [current_price], "$slice": -288}}
                            }
                        )

                    # Collect markets needing classification (DON'T INSERT YET)
                    if needs_classification:
                        # Check if already in queue
                        existing_queue_item = await classification_queue.find_one({"market_slug": market_slug})
                        if not existing_queue_item:
                            markets_needing_classification.append({
                                "market_slug": market_slug,
                                "question": market_question,
                                "queued_at": current_time,
                                "attempts": 0
                            })
                            print(f"Market {market_slug} collected for classification queue")

                except Exception as db_error:
                    print(f"Database error processing market {market_slug}: {db_error}")
                    continue

                markets_processed += 1

            except Exception as e:
                print(f"Error processing market {market_id}: {e}")
                continue

        print(f"\n--- STEP 3 COMPLETE: ALL MARKETS PROCESSED ---")
        print(f"Processed {markets_processed} markets")
        print(f"Markets collected for classification: {len(markets_needing_classification)}")

        # STEP 4: BATCH INSERT TO CLASSIFICATION QUEUE (AFTER ALL PROCESSING)
        print(f"\n--- STEP 4: BATCH INSERTING TO CLASSIFICATION QUEUE ---")
        if markets_needing_classification:
            try:
                await classification_queue.insert_many(markets_needing_classification)
                print(f"Successfully added {len(markets_needing_classification)} markets to classification queue")
            except Exception as queue_error:
                print(f"Error adding markets to queue: {queue_error}")

        # Final diagnostic: Check queue status
        queue_size = await classification_queue.count_documents({})
        print(f"\nDIAGNOSTIC: Final queue size: {queue_size} markets")

        # STEP 5: START CLASSIFICATION WORKER (ONLY AFTER ALL PROCESSING COMPLETE)
        if queue_size > 0:
            print(f"\n--- STEP 5: STARTING CLASSIFICATION WORKER ---")
            print(f"Starting classification worker to process {queue_size} queued markets")
            await ensure_worker_running()
            print("Classification worker started successfully")
        else:
            print(f"\n--- STEP 5: NO CLASSIFICATION NEEDED ---")
            print("No markets in queue, skipping classification worker")

    except Exception as e:
        print(f"Critical error in refresh_markets: {e}")
        import traceback
        traceback.print_exc()
    finally:
        refresh_in_progress = False

async def check_database_integrity():
    """
    Standalone function to check database integrity and restore from backup if needed.
    This can be scheduled to run periodically, separate from the market refresh cycle.
    """
    print("\n\n" + "="*80)
    print("                DATABASE INTEGRITY CHECK                ")
    print("="*80)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # First, validate database connection
        connection_valid = await validate_connection()
        if not connection_valid:
            print("ERROR: MongoDB connection invalid and reconnection failed.")
            return False
        
        # Get current database size
        db_size = await markets_collection.count_documents({})
        print(f"Current database size: {db_size} markets")
        
        # Load last known size from file
        last_size = await async_load_db_size()
        print(f"Last recorded database size: {last_size if last_size else 'None'}")
        
        # Check for catastrophic data loss
        if last_size and last_size > 100 and db_size < last_size * DB_SIZE_DROP_THRESHOLD:
            print(f"⚠️ CRITICAL: DATABASE INTEGRITY COMPROMISED!")
            print(f"Previous size: {last_size}, Current size: {db_size}")
            print(f"Data loss: {last_size - db_size} markets ({((last_size - db_size) / last_size * 100):.1f}%)")
            
            # Backup current state first (even if corrupted)
            emergency_backup = await create_database_backup(max_backups=10)
            print(f"Emergency backup created at: {emergency_backup}")
            
            # Start restoration process
            print("Starting database restoration from most recent backup...")
            restore_success = await restore_from_backup()
            
            if restore_success:
                # Verify restoration
                restored_size = await markets_collection.count_documents({})
                print(f"Database size after restoration: {restored_size}")
                
                if restored_size > db_size:
                    print(f"✅ RESTORATION SUCCESSFUL: Recovered {restored_size - db_size} markets")
                    
                    # Update tracked size with restored value
                    await async_save_db_size(restored_size)
                    return True
                else:
                    print("⚠️ RESTORATION COMPLETED BUT DATA WASN'T RECOVERED")
                    return False
            else:
                print("❌ RESTORATION FAILED")
                return False
        
        # If database is unexpectedly small but not catastrophically reduced
        elif db_size < 10 and (last_size is None or last_size > 20):
            print("WARNING: Database appears to be nearly empty.")
            
            # Try repair first
            print("Attempting repair...")
            repair_result = await repair_database()
            print(f"Repair completed with result: {repair_result}")
            
            # Check if repair helped
            repaired_size = await markets_collection.count_documents({})
            if repaired_size > db_size:
                print(f"Repair recovered some data. New size: {repaired_size}")
            else:
                # Try restoration
                print("Repair didn't help. Attempting restoration from backup...")
                restore_result = await restore_from_backup()
                
                if restore_result:
                    restored_size = await markets_collection.count_documents({})
                    print(f"Restoration complete. New size: {restored_size}")
                    
                    if restored_size > db_size:
                        print("✅ RESTORATION SUCCESSFUL")
                        await async_save_db_size(restored_size)
                        return True
        
        # Database appears healthy
        else:
            print("Database appears to be in good health.")
            
            # Only update size record if database has meaningful data
            if db_size > 10:
                await async_save_db_size(db_size)
                
            return True
            
    except Exception as e:
        print(f"Error checking database integrity: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    print("="*80)

async def start_db_integrity_monitor(check_interval_minutes=30):
    """
    Start a background task that periodically checks database integrity.
    
    Args:
        check_interval_minutes: How often to check database integrity
    """
    print(f"Starting database integrity monitor (checks every {check_interval_minutes} minutes)")
    
    while True:
        try:
            # Wait first to avoid checking right after startup
            await asyncio.sleep(check_interval_minutes * 60)
            
            # Run integrity check
            await check_database_integrity()
            
        except Exception as e:
            print(f"Error in integrity monitor: {e}")
            # If there's an error, wait a bit before trying again
            await asyncio.sleep(300)  # 5 minutes


async def get_max_volatility(record_num: int, categories=None, filter_volume=True, filter_spread=False):
    """
    Get the top `record_num` markets with the highest volatility from the provided in-memory list.
    Optionally filter by categories, volume, and spread.

    Args:
        record_num: Number of top markets to return
        categories: List of categories to filter by
        filter_volume: If True, exclude markets with volume < $1000
        filter_spread: If True, exclude markets with spread <= 0.2
    """
    try:
        if categories:
            print(f"Computing top {record_num} markets by volatility from categories: {categories}")
        else:
            print(f"Computing top {record_num} markets by volatility from live data...")

        markets = Widget_data

        # Filter by categories if provided
        if categories:
            # Convert categories to lowercase for case-insensitive comparison
            categories_lower = [cat.lower() for cat in categories]

            # Filter markets that have valid classifications (not "N/A") and match selected categories
            classified_markets = [m for m in markets if m.get("classification", "N/A") != "N/A"]
            print(f"Total markets with valid classifications: {len(classified_markets)}")

            # Handle "other" category specially
            if "other" in categories_lower:
                # Include markets that don't match known categories or have no classification
                known_categories = ["sports", "politics", "business", "culture", "crypto", "tweets", "weather"]
                other_categories = [cat for cat in categories_lower if cat != "other"]

                markets = []
                # Add markets matching specific categories
                if other_categories:
                    markets.extend([m for m in classified_markets if m.get("classification", "").lower() in other_categories])

                # Add markets that are classified but don't match known categories (for "other")
                markets.extend([m for m in classified_markets
                              if m.get("classification", "").lower() not in known_categories])
            else:
                # Only include markets with classifications matching selected categories
                markets = [m for m in classified_markets if m.get("classification", "").lower() in categories_lower]

            print(f"Found {len(markets)} markets in selected categories: {categories}")

            # Log some examples for debugging  
            if markets:
                print("Sample filtered markets:")
                for i, market in enumerate(markets[:3]):
                    print(f"  {i+1}. {market.get('question', 'N/A')[:50]}... - Category: {market.get('classification', 'N/A')}")
        else:
            print(f"No category filter applied, using all {len(markets)} markets")

        # Ensure each market has a valid 'volatility' and odds between 0% and 98% 
        valid_markets = []
        filtered_out_count = 0

        for m in markets:
            if isinstance(m.get("volatility"), (int, float)):
                odds = m.get("odds")

                # Handle None, null, or missing odds
                if odds is None or odds == "":
                    print(f"FILTERED OUT - Missing odds: {m.get('question', 'N/A')[:50]}... - Odds: {odds}")
                    filtered_out_count += 1
                    continue

                # Convert to float if it's a string
                try:
                    odds = float(odds)
                except (ValueError, TypeError):
                    print(f"FILTERED OUT - Invalid odds format: {m.get('question', 'N/A')[:50]}... - Odds: {odds}")
                    filtered_out_count += 1
                    continue

                # Convert odds to percentage if it's in decimal format (0-1)
                if odds <= 1:
                    odds_percentage = odds * 100
                else:
                    odds_percentage = odds

                # Filter: odds must be > 0.5% and < 98% (strict filtering)
                # This ensures that when rounded in frontend, it shows at least 1%
                if odds_percentage > 0.5 and odds_percentage < 98:
                    valid_markets.append(m)
                    print(f"INCLUDED - {m.get('question', 'N/A')[:50]}... - Odds: {odds_percentage:.2f}%")
                else:
                    print(f"FILTERED OUT - Odds outside range: {m.get('question', 'N/A')[:50]}... - Odds: {odds_percentage:.4f}% (Raw: {odds})")
                    filtered_out_count += 1

        print(f"Markets after odds filter (0% < odds < 98%): {len(valid_markets)}")
        print(f"Markets filtered out due to odds: {filtered_out_count}")

        # Apply volume and spread filters
        final_markets = []
        volume_filtered_count = 0
        spread_filtered_count = 0

        for m in valid_markets:
            # Apply volume filter if enabled
            if filter_volume:
                volume = float(m.get("volume", 0) or 0)
                if volume < 1000:
                    print(f"FILTERED OUT - Volume < $1000: {m.get('question', 'N/A')[:50]}... - Volume: ${volume:,.2f}")
                    volume_filtered_count += 1
                    continue

            # Apply spread filter if enabled
            if filter_spread:
                spread = float(m.get("spread", 0) or 0)
                if spread <= 0.2:
                    print(f"FILTERED OUT - Spread <= 0.2: {m.get('question', 'N/A')[:50]}... - Spread: {spread}")
                    spread_filtered_count += 1
                    continue

            final_markets.append(m)

        print(f"Markets after volume filter (>= $1000): {len(final_markets)}")
        print(f"Markets filtered out due to volume: {volume_filtered_count}")
        print(f"Markets filtered out due to spread: {spread_filtered_count}")

        # Sort by volatility descending and take top records
        top_markets = sorted(final_markets, key=lambda x: x["volatility"], reverse=True)[:record_num]

        # Logging (optional)
        for i, market in enumerate(top_markets, 1):
            odds = market.get('odds', 0)
            odds_percentage = odds * 100 if odds <= 1 else odds
            print(f"{i}. {market.get('market_slug', 'N/A')} - Volatility: {market.get('volatility')} - Odds: {odds_percentage:.1f}% - Category: {market.get('classification', 'N/A')}")
            print(f"   Question: {market.get('question', 'N/A')}")
            print(f"   Last updated: {market.get('lastUpdated', 'N/A')}")

        return top_markets

    except Exception as e:
        print(f"Error computing max volatility: {e}")
        import traceback
        traceback.print_exc()
        return []



async def get_max_volume(record_num: int, categories=None, filter_volume=True, filter_spread=False):
    """
    Get the top `record_num` markets with the highest volume from the provided in-memory list.
    Optionally filter by categories, volume, and spread.

    Args:
        record_num: Number of top markets to return
        categories: List of categories to filter by
        filter_volume: If True, exclude markets with volume < $1000
        filter_spread: If True, exclude markets with spread <= 0.2
    """
    try:
        if categories:
            print(f"Computing top {record_num} markets by volume from categories: {categories}")
        else:
            print(f"Computing top {record_num} markets by volume from live data...")

        markets = Widget_data

        # Filter by categories if provided
        if categories:
            # Convert categories to lowercase for case-insensitive comparison
            categories_lower = [cat.lower() for cat in categories]

            # Filter markets that have valid classifications (not "N/A") and match selected categories
            classified_markets = [m for m in markets if m.get("classification", "N/A") != "N/A"]
            print(f"Total markets with valid classifications: {len(classified_markets)}")

            # Handle "other" category specially
            if "other" in categories_lower:
                # Include markets that don't match known categories or have no classification
                known_categories = ["sports", "politics", "business", "culture", "crypto", "tweets", "weather"]
                other_categories = [cat for cat in categories_lower if cat != "other"]

                markets = []
                # Add markets matching specific categories
                if other_categories:
                    markets.extend([m for m in classified_markets if m.get("classification", "").lower() in other_categories])

                # Add markets that are classified but don't match known categories (for "other")
                markets.extend([m for m in classified_markets
                              if m.get("classification", "").lower() not in known_categories])
            else:
                # Only include markets with classifications matching selected categories
                markets = [m for m in classified_markets if m.get("classification", "").lower() in categories_lower]

            print(f"Found {len(markets)} markets in selected categories: {categories}")

            # Log some examples for debugging
            if markets:
                print("Sample filtered markets:")
                for i, market in enumerate(markets[:3]):
                    print(f"  {i+1}. {market.get('question', 'N/A')[:50]}... - Category: {market.get('classification', 'N/A')}")
        else:
            print(f"No category filter applied, using all {len(markets)} markets")

        # Filter markets by odds (must be > 0% and < 98%) and valid volume
        valid_markets = []
        filtered_out_count = 0

        for m in markets:
            if m.get("volume") is not None:
                odds = m.get("odds")

                # Handle None, null, or missing odds
                if odds is None or odds == "":
                    print(f"FILTERED OUT - Missing odds: {m.get('question', 'N/A')[:50]}... - Odds: {odds}")
                    filtered_out_count += 1
                    continue

                # Convert to float if it's a string
                try:
                    odds = float(odds)
                except (ValueError, TypeError):
                    print(f"FILTERED OUT - Invalid odds format: {m.get('question', 'N/A')[:50]}... - Odds: {odds}")
                    filtered_out_count += 1
                    continue

                # Convert odds to percentage if it's in decimal format (0-1)
                if odds <= 1:
                    odds_percentage = odds * 100
                else:
                    odds_percentage = odds

                # Filter: odds must be > 0.5% and < 98% (strict filtering)
                # This ensures that when rounded in frontend, it shows at least 1%
                if odds_percentage > 0.5 and odds_percentage < 98:
                    valid_markets.append(m)
                    print(f"INCLUDED - {m.get('question', 'N/A')[:50]}... - Odds: {odds_percentage:.2f}%")
                else:
                    print(f"FILTERED OUT - Odds outside range: {m.get('question', 'N/A')[:50]}... - Odds: {odds_percentage:.4f}% (Raw: {odds})")
                    filtered_out_count += 1

        print(f"Markets after odds filter (0% < odds < 98%): {len(valid_markets)}")
        print(f"Markets filtered out due to odds: {filtered_out_count}")

        # Apply volume and spread filters
        final_markets = []
        volume_filtered_count = 0
        spread_filtered_count = 0

        for m in valid_markets:
            # Apply volume filter if enabled
            if filter_volume:
                volume = float(m.get("volume", 0) or 0)
                if volume < 1000:
                    print(f"FILTERED OUT - Volume < $1000: {m.get('question', 'N/A')[:50]}... - Volume: ${volume:,.2f}")
                    volume_filtered_count += 1
                    continue

            # Apply spread filter if enabled
            if filter_spread:
                spread = float(m.get("spread", 0) or 0)
                if spread <= 0.2:
                    print(f"FILTERED OUT - Spread <= 0.2: {m.get('question', 'N/A')[:50]}... - Spread: {spread}")
                    spread_filtered_count += 1
                    continue

            final_markets.append(m)

        print(f"Markets after volume filter (>= $1000): {len(final_markets)}")
        print(f"Markets filtered out due to volume: {volume_filtered_count}")
        print(f"Markets filtered out due to spread: {spread_filtered_count}")

        # Sort by volume descending and take top records
        top_markets = sorted(
            final_markets,
            key=lambda m: float(m.get("volume", 0) or 0),
            reverse=True
        )[:record_num]

        # Logging (optional)
        for i, market in enumerate(top_markets, 1):
            odds = market.get('odds', 0)
            odds_percentage = odds * 100 if odds <= 1 else odds
            print(f"{i}. {market.get('market_slug', 'N/A')} - Volume: {market.get('volume')} - Odds: {odds_percentage:.1f}% - Category: {market.get('classification', 'N/A')}")
            print(f"   Question: {market.get('question', 'N/A')}")

        return top_markets

    except Exception as e:
        print(f"Error computing max volume: {e}")
        import traceback
        traceback.print_exc()
        return []

async def update_market_impact_background(market_slug: str, question: str):
    """
    Run impact analysis in background and update market when complete
    """
    try:
        print(f"Starting background impact analysis for {market_slug}")
        impact_data = await deep_seek_analysis_ai(question)
        
        # Update the market with impact data
        await markets_collection.update_one(
            {"market_slug": market_slug},
            {"$set": {
                "moneybag": impact_data.get("moneybag", False),
                "ox": impact_data.get("ox", []),
                "bear": impact_data.get("bear", [])
            }}
        )
        print(f"Background impact analysis complete for {market_slug}")
    except Exception as e:
        print(f"Background impact analysis failed for {market_slug}: {e}")

