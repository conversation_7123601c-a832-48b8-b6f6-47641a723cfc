from fastapi import FastAPI, BackgroundTasks, Query, Request 
from fastapi.middleware.cors import CORSMiddleware
from tasks import refresh_markets, fetch_markets , get_max_volatility, get_max_volume
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from pydantic import BaseModel, Field, HttpUrl, BeforeValidator
from bson import ObjectId
from mongo_manager import markets_collection, classification_queue
from datetime import datetime
from middleware import CustomAuthMiddleware
from typing import Optional, Annotated, List 
from asyncio import run, create_task
from datetime import datetime, timedelta, timezone
from classification_worker import ensure_worker_running, process_classification_queue
from tasks import Widget_data  # Import the new global data cache


app = FastAPI()

# app.add_middleware(CustomAuthMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Schedule periodic refresh every 5 minutes

# scheduler = AsyncIOScheduler()
# scheduler.add_job(refresh_markets, trigger='interval', minutes=5)
# scheduler.start()
scheduler = AsyncIOScheduler()

def normalize(obj):
    obj['_id'] = str(obj['_id'])
    return obj 

PyObjectId = Annotated[str, BeforeValidator(str)]

class MarketModel(BaseModel):
    id: Optional[PyObjectId] = Field(alias="_id", default=None) # Unique identifier for the market, typically the market slug
    question: str  # The market question
    market_slug: str  # Slug used for the market URL
    volatility: Optional[float] = None  # 2-hour volatility percentage for the market
    volatility_last_24_hours: Optional[float] = None  # 2-hour volatility percentage for the market
    odds_trend: str
    end_date: datetime  # ISO format end date for the market
    icon: Optional[HttpUrl]  # URL for the market icon
    lastUpdated: datetime  # Timestamp of the last update
    classification: Optional[str] = None 
    spread: Optional[float] = None 
    prices: Optional[list] = None 


@app.on_event("startup")
async def startup_event():
    # Set up market refresh scheduler
    scheduler.add_job(refresh_markets, trigger='interval', minutes=5)
    
    # Set up a scheduler for the classification worker check
    scheduler.add_job(ensure_worker_running, trigger='interval', minutes=5)
    
    scheduler.start()
    
    # Kick off an immediate refresh
    create_task(refresh_markets())
    
    # Start the classification worker if needed
    create_task(ensure_worker_running())

@app.on_event("shutdown")
def shutdown_event():
    scheduler.shutdown()

@app.get("/api/markets")
async def fetch_markets_view(
    request: Request,
    volatility: str = Query(None),
    volume: str = Query(None)
):
    try:
        # Create timezone-aware threshold
        fresh_threshold = datetime.now(timezone.utc) - timedelta(minutes=10)
        print(f"Fresh threshold set to: {fresh_threshold}")
        
        # First get all markets to check freshness
        all_markets = await markets_collection.find({}).to_list(None)
        print(f"Found {len(all_markets)} total markets")
        
        # Filter for fresh markets and ensure lastUpdated is a timezone-aware datetime 
        fresh_markets = []
        for market in all_markets:
            try:
                last_updated = market.get("lastUpdated")
                print(f"Processing market with lastUpdated: {last_updated} (type: {type(last_updated)})")
                
                # Convert to timezone-aware datetime if needed
                if isinstance(last_updated, dict) and "$date" in last_updated:
                    # Handle MongoDB date format
                    last_updated = datetime.fromisoformat(last_updated["$date"].replace("Z", "+00:00"))
                    print(f"Converted MongoDB date format to: {last_updated}")
                elif isinstance(last_updated, str):
                    # Handle string format
                    last_updated = datetime.fromisoformat(last_updated.replace("Z", "+00:00"))
                    print(f"Converted string format to: {last_updated}")
                elif isinstance(last_updated, datetime):
                    # If it's already a datetime but not timezone-aware, make it UTC
                    if last_updated.tzinfo is None:
                        last_updated = last_updated.replace(tzinfo=timezone.utc)
                        print(f"Added timezone to naive datetime: {last_updated}")
                else:
                    print(f"Skipping market with invalid lastUpdated type: {type(last_updated)}")
                    continue
                
                # Ensure both values are timezone-aware before comparison
                if not isinstance(last_updated, datetime) or last_updated.tzinfo is None:
                    print(f"Invalid datetime after conversion: {last_updated}")
                    continue
                
                market["lastUpdated"] = last_updated
                if last_updated >= fresh_threshold:
                    fresh_markets.append(market)
                    print(f"Added fresh market updated at: {last_updated}")
            except Exception as e:
                print(f"Error processing market: {e}")
                continue
        
        print(f"Found {len(fresh_markets)} fresh markets")
        
        # If no fresh markets, use all markets
        data = fresh_markets if fresh_markets else all_markets
        print(f"Using {len(data)} markets for response")
        
        # Process the data
        for obj in data:
            obj["volatility"] = round(obj.get("volatility", 0.0), 5)
            obj["_id"] = str(obj["_id"])

        # Sorting logic based on query parameters
        if volume == "high":
            def get_sort_key(x):
                last_updated = x.get("lastUpdated", datetime.min.replace(tzinfo=timezone.utc))
                # Convert dict format to datetime if needed
                if isinstance(last_updated, dict) and "$date" in last_updated:
                    last_updated = datetime.fromisoformat(last_updated["$date"].replace("Z", "+00:00"))
                elif isinstance(last_updated, str):
                    last_updated = datetime.fromisoformat(last_updated.replace("Z", "+00:00"))
                elif isinstance(last_updated, datetime) and last_updated.tzinfo is None:
                    last_updated = last_updated.replace(tzinfo=timezone.utc)
                return (float(x.get("volume", 0)), last_updated)
            
            sorted_markets = sorted(data, key=get_sort_key, reverse=True)
        elif volume == "low":
            def get_sort_key(x):
                last_updated = x.get("lastUpdated", datetime.min.replace(tzinfo=timezone.utc))
                if isinstance(last_updated, dict) and "$date" in last_updated:
                    last_updated = datetime.fromisoformat(last_updated["$date"].replace("Z", "+00:00"))
                elif isinstance(last_updated, str):
                    last_updated = datetime.fromisoformat(last_updated.replace("Z", "+00:00"))
                elif isinstance(last_updated, datetime) and last_updated.tzinfo is None:
                    last_updated = last_updated.replace(tzinfo=timezone.utc)
                return (float(x.get("volume", 0)), last_updated)
            
            sorted_markets = sorted(data, key=get_sort_key)
        elif volatility == "high":
            sorted_markets = sorted(data, key=lambda x: x.get("volatility", 0.0), reverse=True)
        elif volatility == "low":
            sorted_markets = sorted(data, key=lambda x: x.get("volatility", 0.0))
        else:
            # Default sort: by lastUpdated descending (latest first)
            sorted_markets = sorted(
                data,
                key=lambda x: x.get("lastUpdated", datetime.min.replace(tzinfo=timezone.utc)),
                reverse=True
            )

        return sorted_markets
    except Exception as e:
        print(f"Error in fetch_markets_view: {e}")
        import traceback
        print(traceback.format_exc())
        raise


@app.post("/api/refresh")
async def manual_refresh(request: Request, background_tasks: BackgroundTasks):
    background_tasks.add_task(refresh_markets)
    return {"message": "Refresh started in the background."}

@app.get("/api/top-5-moves")
async def get_top_5_moves(request: Request):
    cursor = markets_collection.find().sort("hot", -1).limit(4)
    top_5_records = await cursor.to_list(length=5)
    for x in top_5_records:
        x['hot'] = round(x.get('hot', 0.0), 3)
        x['_id'] = str(x['_id'])
    return top_5_records

# @app.delete("/api/reset-db/")
# async def reset_db(request: Request):
#     await markets_collection.delete_many({})

#     return {
#         "message": "db reset"
#     }

@app.get("/api/health-check")
async def health_check(request: Request):
    return {"message": "health check passed"}

@app.get("/api/classification-queue")
async def get_classification_queue_status():
    """Get the status of the classification queue"""
    queue_size = await classification_queue.count_documents({})
    
    # Get stats on queue items
    pending_items = await classification_queue.count_documents({"attempts": 0})
    retry_items = await classification_queue.count_documents({"attempts": {"$gt": 0}})
    
    # Get a sample of recent items
    recent_items = await classification_queue.find().sort("queued_at", -1).limit(5).to_list(5)
    for item in recent_items:
        item["_id"] = str(item["_id"])
    
    return {
        "queue_size": queue_size,
        "pending": pending_items,
        "retries": retry_items,
        "recent_items": recent_items
    }

@app.post("/api/process-classification-queue")
async def trigger_classification_worker(background_tasks: BackgroundTasks):
    """Manually trigger the classification worker"""
    background_tasks.add_task(process_classification_queue)
    return {"message": "Classification processing started"}

@app.get("/api/maintenance/fix-pending-classifications")
async def fix_pending_classifications():
    """
    Find all markets with 'Pending' classification and convert them to 'N/A',
    then add them to the queue if they're not already there.
    """
    # Find all markets with 'Pending' classification
    pending_markets = await markets_collection.find({"classification": "Pending"}).to_list(1000)
    
    converted_to_na = 0
    added_to_queue = 0
    already_in_queue = 0
    
    for market in pending_markets:
        market_slug = market.get("market_slug")
        question = market.get("question")
        
        # Convert from Pending to N/A
        await markets_collection.update_one(
            {"market_slug": market_slug},
            {"$set": {"classification": "N/A"}}
        )
        converted_to_na += 1
        
        # Check if already in queue
        existing_in_queue = await classification_queue.find_one({"market_slug": market_slug})
        
        if not existing_in_queue:
            # Add to classification queue
            await classification_queue.insert_one({
                "market_slug": market_slug,
                "question": question,
                "queued_at": datetime.now(timezone.utc),
                "attempts": 0
            })
            added_to_queue += 1
        else:
            already_in_queue += 1
    
    # Start the worker if needed
    if added_to_queue > 0:
        await ensure_worker_running()
    
    return {
        "message": "Fixed pending classifications",
        "found_pending": len(pending_markets),
        "converted_to_na": converted_to_na,
        "added_to_queue": added_to_queue,
        "already_in_queue": already_in_queue
    }

@app.get("/api/maintenance/cleanup-orphaned-queue-items")
async def cleanup_orphaned_queue_items():
    """
    Find and remove queue items that don't have corresponding markets in the database.
    This helps clean up the queue when markets have been removed from the source.
    """
    queue_items = await classification_queue.find({}).to_list(1000)
    
    removed_count = 0
    market_found_count = 0
    error_count = 0
    
    for item in queue_items:
        try:
            market_slug = item.get("market_slug")
            if not market_slug:
                await classification_queue.delete_one({"_id": item["_id"]})
                removed_count += 1
                continue
                
            # Try finding the market in various formats
            market_exists = False
            
            # Try string format
            market = await markets_collection.find_one({"market_slug": market_slug})
            if market:
                market_exists = True
            else:
                # Try numeric format
                try:
                    numeric_id = int(market_slug)
                    market = await markets_collection.find_one({"market_slug": numeric_id})
                    if market:
                        market_exists = True
                except (ValueError, TypeError):
                    pass
                    
                # Try by question
                if not market_exists:
                    question = item.get("question")
                    if question:
                        market = await markets_collection.find_one({"question": question})
                        if market:
                            market_exists = True
            
            if not market_exists:
                # Market doesn't exist in any format, remove from queue
                await classification_queue.delete_one({"_id": item["_id"]})
                removed_count += 1
            else:
                market_found_count += 1
                
        except Exception as e:
            error_count += 1
            print(f"Error processing queue item: {e}")
    
    return {
        "message": "Orphaned queue items cleanup completed",
        "total_processed": len(queue_items),
        "orphaned_removed": removed_count,
        "markets_found": market_found_count,
        "errors": error_count
    }

@app.get("/api/maintenance/sync-mismatched-classifications")
async def sync_mismatched_classifications():
    """
    Find markets where the classification might be stuck in N/A but the queue
    item was processed. This helps sync any classifications that weren't properly saved.
    """
    # First, get all markets with N/A classification
    na_markets = await markets_collection.find({"classification": "N/A"}).to_list(1000)
    
    # Track results
    checked_count = 0
    fixed_count = 0
    errors = 0
    
    # Get queue items with at least 1 attempt (processed but possibly not updated)
    processed_items = await classification_queue.find({"attempts": {"$gt": 0}}).to_list(1000)
    processed_dict = {item.get("market_slug"): item for item in processed_items}
    
    # Process each N/A market
    for market in na_markets:
        try:
            checked_count += 1
            market_slug = market.get("market_slug")
            
            # Check if this market has a processed queue item
            processed_item = processed_dict.get(market_slug) or processed_dict.get(str(market_slug)) or processed_dict.get(int(market_slug)) if isinstance(market_slug, str) and market_slug.isdigit() else None
            
            if processed_item and processed_item.get("last_error"):
                # This market was processed but had an error, requeue it
                existing = await classification_queue.find_one({"market_slug": market_slug, "attempts": 0})
                if not existing:
                    # Re-add to queue with reset attempts
                    await classification_queue.update_one(
                        {"_id": processed_item["_id"]},
                        {"$set": {"attempts": 0, "queued_at": datetime.now(timezone.utc)}}
                    )
                    print(f"Requeued market {market_slug} that had previous errors")
                    fixed_count += 1
                    
            # Look for this market's question in successfully processed items
            found_match = False
            market_question = market.get("question")
            
            if market_question:
                for item in processed_items:
                    if item.get("question") == market_question and "last_error" not in item:
                        # This question was successfully processed but the market wasn't updated
                        # Re-add it to the queue with reset attempts
                        existing = await classification_queue.find_one({"question": market_question, "attempts": 0})
                        if not existing:
                            await classification_queue.insert_one({
                                "market_slug": market_slug,
                                "question": market_question,
                                "queued_at": datetime.now(timezone.utc),
                                "attempts": 0
                            })
                            print(f"Added market {market_slug} back to queue - question was processed but classification wasn't updated")
                            fixed_count += 1
                            found_match = True
                            break
            
            # If no match found, check if this market is in the queue
            if not found_match:
                existing = await classification_queue.find_one({"market_slug": market_slug})
                if not existing:
                    # Add to queue as new item
                    await classification_queue.insert_one({
                        "market_slug": market_slug,
                        "question": market.get("question"),
                        "queued_at": datetime.now(timezone.utc),
                        "attempts": 0
                    })
                    print(f"Added missing market {market_slug} to classification queue")
                    fixed_count += 1
                    
        except Exception as e:
            print(f"Error processing market during sync: {e}")
            errors += 1
    
    # Start the worker if we added items to the queue
    if fixed_count > 0:
        await ensure_worker_running()
    
    return {
        "message": "Classification sync completed",
        "markets_checked": checked_count,
        "markets_fixed": fixed_count,
        "errors": errors
    }



from fastapi import HTTPException
from httpx import AsyncClient

@app.get("/api/pro_markets")
async def get_volatile_markets(request: Request):
    try:
        async with AsyncClient(base_url=str(request.base_url)) as client:
            response = await client.get("/api/markets")
            if response.status_code != 200:
                raise HTTPException(status_code=500, detail="Failed to fetch markets")
            markets_data = response.json()

        # Filter markets where volatility > 0 and moneybag is True
        volatile_markets = []
        for market in markets_data:
            try:
                vol = float(market.get("volatility", 0))
                moneybay = str(market.get("moneybag"))
                if vol > 0 and moneybay == 'True':
                    volatile_markets.append(market)
            except (ValueError, TypeError):
                continue

        return volatile_markets

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching pro markets: {str(e)}")



@app.get("/api/debug/impact-status")
async def impact_status():
    total = await markets_collection.count_documents({})
    with_fields = await markets_collection.count_documents({
        "moneybag": {"$exists": True},
        "ox":       {"$exists": True},
        "bear":     {"$exists": True}
    })
    true_moneybag = await markets_collection.count_documents({"moneybag": True})
    # you can check non‐empty arrays, too:
    nonempty_ox = await markets_collection.count_documents({"ox.0": {"$exists": True}})
    nonempty_bear = await markets_collection.count_documents({"bear.0": {"$exists": True}})

    return {
        "total_docs": total,
        "have_all_fields": with_fields,
        "moneybag_true": true_moneybag,
        "ox_nonempty": nonempty_ox,
        "bear_nonempty": nonempty_bear
    }

@app.get("/api/max_volatility")
async def max_volatility_endpoint(
    categories: str = None,
    limit: int = 10,
    filter_volume: bool = True,
    filter_spread: bool = False
):
    """
    API endpoint to get top markets by volatility, optionally filtered by categories.
    categories: comma-separated string like "crypto,politics,business"
    limit: number of markets to return (default: 10)
    filter_volume: whether to exclude markets with volume < $1000 (default: True)
    filter_spread: whether to exclude markets with spread <= 0.2 (default: False)
    """
    try:
        # Parse categories if provided
        category_list = None
        if categories:
            category_list = [cat.strip() for cat in categories.split(',')]
            print(f"API: Filtering by categories: {category_list}")

        if Widget_data:
            print(f"Widget_data has {len(Widget_data)} entries")
            if category_list:
                print(f"Filtering by categories: {category_list}")
        else:
            print("###############################\n")
            print("Widget_data is empty")
            print("###############################\n")

        return {"top_volatility_markets": await get_max_volatility(limit, category_list, filter_volume, filter_spread)}
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {"error": f"Internal server error: {str(e)}"}



@app.get("/api/max_volume")
async def max_volume_endpoint(
    categories: str = None,
    limit: int = 10,
    filter_volume: bool = True,
    filter_spread: bool = False
):
    """
    API endpoint to get top markets by volume, optionally filtered by categories.
    categories: comma-separated string like "crypto,politics,business"
    limit: number of markets to return (default: 10)
    filter_volume: whether to exclude markets with volume < $1000 (default: True)
    filter_spread: whether to exclude markets with spread <= 0.2 (default: False)
    """
    try:
        # Parse categories if provided
        category_list = None
        if categories:
            category_list = [cat.strip() for cat in categories.split(',')]
            print(f"API: Filtering by categories: {category_list}")

        if Widget_data:
            print(f"Widget_data has {len(Widget_data)} entries")
            if category_list:
                print(f"Filtering by categories: {category_list}")
        else:
            print("###############################\n")
            print("Widget_data is empty")
            print("###############################\n")

        return {"top_volume_markets": await get_max_volume(limit, category_list, filter_volume, filter_spread)}
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {"error": f"Internal server error: {str(e)}"}
