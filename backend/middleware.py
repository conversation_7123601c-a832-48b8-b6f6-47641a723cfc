from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request 
from fastapi.responses import JSONResponse


class CustomAuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        allowed_origins = ['https://polyvol.info'
                           ,"*"]

        origin = request.headers.get("origin")
        referer = request.headers.get("referer")

        # Check if the request is from an allowed origin 
        if origin and origin in allowed_origins:
            return await call_next(request)
        
        elif referer and any(referer.startswith(origin) for origin in allowed_origins):
            return await call_next(request)
        
        else:
            return JSONResponse(content={
                "message": "Your Unauthorized Attempt to Access the System has been Logged, Divert now"
            }, status_code=401)
            

