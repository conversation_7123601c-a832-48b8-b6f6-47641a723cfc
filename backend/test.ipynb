{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching markets from Polymarket API...\n", "\n", "Fetching batch starting at offset 0...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: GET https://gamma-api.polymarket.com/markets?limit=500&offset=0&closed=false \"HTTP/1.1 200 OK\"\n"]}, {"ename": "CancelledError", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mCancelledError\u001b[0m                            Traceback (most recent call last)", "Cell \u001b[0;32mIn[3], line 54\u001b[0m\n\u001b[1;32m     51\u001b[0m             \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m     53\u001b[0m \u001b[38;5;66;03m# Run directly in Jupyter\u001b[39;00m\n\u001b[0;32m---> 54\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>t\u001b[39;00m fetch_and_print_markets()\n", "Cell \u001b[0;32mIn[3], line 25\u001b[0m, in \u001b[0;36mfetch_and_print_markets\u001b[0;34m()\u001b[0m\n\u001b[1;32m     22\u001b[0m url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mBASE_API_URL\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m?limit=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mlimit\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m&offset=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moffset\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m&closed=false\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFetching batch starting at offset \u001b[39m\u001b[38;5;132;01m{\u001b[39;00moffset\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 25\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m client\u001b[38;5;241m.\u001b[39mget(url, timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m10.0\u001b[39m)\n\u001b[1;32m     26\u001b[0m response\u001b[38;5;241m.\u001b[39mraise_for_status()\n\u001b[1;32m     28\u001b[0m batch \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mjson()\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_client.py:1801\u001b[0m, in \u001b[0;36mAsyncClient.get\u001b[0;34m(self, url, params, headers, cookies, auth, follow_redirects, timeout, extensions)\u001b[0m\n\u001b[1;32m   1784\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget\u001b[39m(\n\u001b[1;32m   1785\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1786\u001b[0m     url: URLTypes,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1794\u001b[0m     extensions: RequestExtensions \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1795\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Response:\n\u001b[1;32m   1796\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1797\u001b[0m \u001b[38;5;124;03m    Send a `GET` request.\u001b[39;00m\n\u001b[1;32m   1798\u001b[0m \n\u001b[1;32m   1799\u001b[0m \u001b[38;5;124;03m    **Parameters**: See `httpx.request`.\u001b[39;00m\n\u001b[1;32m   1800\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1801\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(\n\u001b[1;32m   1802\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGET\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   1803\u001b[0m         url,\n\u001b[1;32m   1804\u001b[0m         params\u001b[38;5;241m=\u001b[39mparams,\n\u001b[1;32m   1805\u001b[0m         headers\u001b[38;5;241m=\u001b[39mheaders,\n\u001b[1;32m   1806\u001b[0m         cookies\u001b[38;5;241m=\u001b[39mcookies,\n\u001b[1;32m   1807\u001b[0m         auth\u001b[38;5;241m=\u001b[39mauth,\n\u001b[1;32m   1808\u001b[0m         follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects,\n\u001b[1;32m   1809\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[1;32m   1810\u001b[0m         extensions\u001b[38;5;241m=\u001b[39mextensions,\n\u001b[1;32m   1811\u001b[0m     )\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_client.py:1574\u001b[0m, in \u001b[0;36mAsyncClient.request\u001b[0;34m(self, method, url, content, data, files, json, params, headers, cookies, auth, follow_redirects, timeout, extensions)\u001b[0m\n\u001b[1;32m   1559\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(message, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m)\n\u001b[1;32m   1561\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuild_request(\n\u001b[1;32m   1562\u001b[0m     method\u001b[38;5;241m=\u001b[39mmethod,\n\u001b[1;32m   1563\u001b[0m     url\u001b[38;5;241m=\u001b[39murl,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1572\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mextensions,\n\u001b[1;32m   1573\u001b[0m )\n\u001b[0;32m-> 1574\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend(request, auth\u001b[38;5;241m=\u001b[39mauth, follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects)\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_client.py:1675\u001b[0m, in \u001b[0;36mAsyncClient.send\u001b[0;34m(self, request, stream, auth, follow_redirects)\u001b[0m\n\u001b[1;32m   1673\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m   1674\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m response\u001b[38;5;241m.\u001b[39maclose()\n\u001b[0;32m-> 1675\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_client.py:1669\u001b[0m, in \u001b[0;36mAsyncClient.send\u001b[0;34m(self, request, stream, auth, follow_redirects)\u001b[0m\n\u001b[1;32m   1667\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1668\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n\u001b[0;32m-> 1669\u001b[0m         \u001b[38;5;28;01mawait\u001b[39;00m response\u001b[38;5;241m.\u001b[39maread()\n\u001b[1;32m   1671\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n\u001b[1;32m   1673\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_models.py:911\u001b[0m, in \u001b[0;36mResponse.aread\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    907\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    908\u001b[0m \u001b[38;5;124;03mRead and return the response content.\u001b[39;00m\n\u001b[1;32m    909\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    910\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_content\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m--> 911\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_content \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin([part \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m part \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maiter_bytes()])\n\u001b[1;32m    912\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_content\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_models.py:911\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m    907\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    908\u001b[0m \u001b[38;5;124;03mRead and return the response content.\u001b[39;00m\n\u001b[1;32m    909\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    910\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_content\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m--> 911\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_content \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin([part \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m part \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maiter_bytes()])\n\u001b[1;32m    912\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_content\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_models.py:929\u001b[0m, in \u001b[0;36mResponse.aiter_bytes\u001b[0;34m(self, chunk_size)\u001b[0m\n\u001b[1;32m    927\u001b[0m chunker \u001b[38;5;241m=\u001b[39m ByteChunker(chunk_size\u001b[38;5;241m=\u001b[39mchunk_size)\n\u001b[1;32m    928\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request):\n\u001b[0;32m--> 929\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m raw_bytes \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maiter_raw():\n\u001b[1;32m    930\u001b[0m         decoded \u001b[38;5;241m=\u001b[39m decoder\u001b[38;5;241m.\u001b[39mdecode(raw_bytes)\n\u001b[1;32m    931\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m chunker\u001b[38;5;241m.\u001b[39mdecode(decoded):\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_models.py:987\u001b[0m, in \u001b[0;36mResponse.aiter_raw\u001b[0;34m(self, chunk_size)\u001b[0m\n\u001b[1;32m    984\u001b[0m chunker \u001b[38;5;241m=\u001b[39m ByteChunker(chunk_size\u001b[38;5;241m=\u001b[39mchunk_size)\n\u001b[1;32m    986\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request):\n\u001b[0;32m--> 987\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m raw_stream_bytes \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstream:\n\u001b[1;32m    988\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_num_bytes_downloaded \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlen\u001b[39m(raw_stream_bytes)\n\u001b[1;32m    989\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m chunker\u001b[38;5;241m.\u001b[39mdecode(raw_stream_bytes):\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_client.py:149\u001b[0m, in \u001b[0;36mBoundAsyncStream.__aiter__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    148\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__aiter__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m typing\u001b[38;5;241m.\u001b[39mAsyncIterator[\u001b[38;5;28mbytes\u001b[39m]:\n\u001b[0;32m--> 149\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_stream:\n\u001b[1;32m    150\u001b[0m         \u001b[38;5;28;01my<PERSON>\u001b[39;00m chunk\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpx/_transports/default.py:254\u001b[0m, in \u001b[0;36mAsyncResponseStream.__aiter__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    252\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__aiter__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m typing\u001b[38;5;241m.\u001b[39mAsyncIterator[\u001b[38;5;28mbytes\u001b[39m]:\n\u001b[1;32m    253\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[0;32m--> 254\u001b[0m         \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m part \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_httpcore_stream:\n\u001b[1;32m    255\u001b[0m             \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m part\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpcore/_async/connection_pool.py:367\u001b[0m, in \u001b[0;36mPoolByteStream.__aiter__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    365\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    366\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maclose()\n\u001b[0;32m--> 367\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpcore/_async/connection_pool.py:363\u001b[0m, in \u001b[0;36mPoolByteStream.__aiter__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    361\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__aiter__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m AsyncIterator[\u001b[38;5;28mbytes\u001b[39m]:\n\u001b[1;32m    362\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 363\u001b[0m         \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m part \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_stream:\n\u001b[1;32m    364\u001b[0m             \u001b[38;5;28;<PERSON><PERSON><PERSON>\u001b[39;00m part\n\u001b[1;32m    365\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpcore/_async/http11.py:349\u001b[0m, in \u001b[0;36mHTTP11ConnectionByteStream.__aiter__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    347\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m AsyncShieldCancellation():\n\u001b[1;32m    348\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maclose()\n\u001b[0;32m--> 349\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpcore/_async/http11.py:341\u001b[0m, in \u001b[0;36mHTTP11ConnectionByteStream.__aiter__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    339\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    340\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreceive_response_body\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request, kwargs):\n\u001b[0;32m--> 341\u001b[0m         \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection\u001b[38;5;241m.\u001b[39m_receive_response_body(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    342\u001b[0m             \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m chunk\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    344\u001b[0m     \u001b[38;5;66;03m# If we get an exception while streaming the response,\u001b[39;00m\n\u001b[1;32m    345\u001b[0m     \u001b[38;5;66;03m# we want to close the response (and possibly the connection)\u001b[39;00m\n\u001b[1;32m    346\u001b[0m     \u001b[38;5;66;03m# before raising that exception.\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpcore/_async/http11.py:210\u001b[0m, in \u001b[0;36mAsyncHTTP11Connection._receive_response_body\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    207\u001b[0m timeout \u001b[38;5;241m=\u001b[39m timeouts\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mread\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    209\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 210\u001b[0m     event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_receive_event(timeout\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    211\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11\u001b[38;5;241m.\u001b[39mData):\n\u001b[1;32m    212\u001b[0m         \u001b[38;5;28;01<PERSON>ield\u001b[39;00m \u001b[38;5;28mbytes\u001b[39m(event\u001b[38;5;241m.\u001b[39mdata)\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpcore/_async/http11.py:224\u001b[0m, in \u001b[0;36mAsyncHTTP11Connection._receive_event\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    221\u001b[0m     event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_h11_state\u001b[38;5;241m.\u001b[39mnext_event()\n\u001b[1;32m    223\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11\u001b[38;5;241m.\u001b[39mNEED_DATA:\n\u001b[0;32m--> 224\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_network_stream\u001b[38;5;241m.\u001b[39mread(\n\u001b[1;32m    225\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mREAD_NUM_BYTES, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[1;32m    226\u001b[0m     )\n\u001b[1;32m    228\u001b[0m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[1;32m    229\u001b[0m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m    230\u001b[0m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    234\u001b[0m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n\u001b[1;32m    236\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m data \u001b[38;5;241m==\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_h11_state\u001b[38;5;241m.\u001b[39mtheir_state \u001b[38;5;241m==\u001b[39m h11\u001b[38;5;241m.\u001b[39mSEND_RESPONSE:\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/httpcore/_backends/anyio.py:35\u001b[0m, in \u001b[0;36mAnyIOStream.read\u001b[0;34m(self, max_bytes, timeout)\u001b[0m\n\u001b[1;32m     33\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m anyio\u001b[38;5;241m.\u001b[39mfail_after(timeout):\n\u001b[1;32m     34\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 35\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_stream\u001b[38;5;241m.\u001b[39mreceive(max_bytes\u001b[38;5;241m=\u001b[39mmax_bytes)\n\u001b[1;32m     36\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m anyio\u001b[38;5;241m.\u001b[39mEndOfStream:  \u001b[38;5;66;03m# pragma: nocover\u001b[39;00m\n\u001b[1;32m     37\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/anyio/streams/tls.py:171\u001b[0m, in \u001b[0;36mTLSStream.receive\u001b[0;34m(self, max_bytes)\u001b[0m\n\u001b[1;32m    170\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mreceive\u001b[39m(\u001b[38;5;28mself\u001b[39m, max_bytes: \u001b[38;5;28mint\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m65536\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mbytes\u001b[39m:\n\u001b[0;32m--> 171\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_call_sslobject_method(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_ssl_object\u001b[38;5;241m.\u001b[39mread, max_bytes)\n\u001b[1;32m    172\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m data:\n\u001b[1;32m    173\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m EndOfStream\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/anyio/streams/tls.py:115\u001b[0m, in \u001b[0;36mTLSStream._call_sslobject_method\u001b[0;34m(self, func, *args)\u001b[0m\n\u001b[1;32m    112\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_write_bio\u001b[38;5;241m.\u001b[39mpending:\n\u001b[1;32m    113\u001b[0m         \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtransport_stream\u001b[38;5;241m.\u001b[39msend(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_write_bio\u001b[38;5;241m.\u001b[39mread())\n\u001b[0;32m--> 115\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtransport_stream\u001b[38;5;241m.\u001b[39mreceive()\n\u001b[1;32m    116\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m EndOfStream:\n\u001b[1;32m    117\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_read_bio\u001b[38;5;241m.\u001b[39mwrite_eof()\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/anyio/_backends/_asyncio.py:1105\u001b[0m, in \u001b[0;36mSocketStream.receive\u001b[0;34m(self, max_bytes)\u001b[0m\n\u001b[1;32m   1103\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_protocol\u001b[38;5;241m.\u001b[39mread_event\u001b[38;5;241m.\u001b[39mis_set() \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_transport\u001b[38;5;241m.\u001b[39mis_closing():\n\u001b[1;32m   1104\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_transport\u001b[38;5;241m.\u001b[39mresume_reading()\n\u001b[0;32m-> 1105\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_protocol\u001b[38;5;241m.\u001b[39mread_event\u001b[38;5;241m.\u001b[39mwait()\n\u001b[1;32m   1106\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_transport\u001b[38;5;241m.\u001b[39mpause_reading()\n\u001b[1;32m   1108\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/asyncio/locks.py:213\u001b[0m, in \u001b[0;36mEvent.wait\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    211\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_waiters\u001b[38;5;241m.\u001b[39mappend(fut)\n\u001b[1;32m    212\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 213\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m fut\n\u001b[1;32m    214\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m    215\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/asyncio/futures.py:287\u001b[0m, in \u001b[0;36mFuture.__await__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    285\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdone():\n\u001b[1;32m    286\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_asyncio_future_blocking \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m--> 287\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m  \u001b[38;5;66;03m# This tells Task to wait for completion.\u001b[39;00m\n\u001b[1;32m    288\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdone():\n\u001b[1;32m    289\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mawait wasn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt used with future\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mCancelledError\u001b[0m: "]}], "source": ["import httpx\n", "import asyncio\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()\n", "\n", "BASE_API_URL = \"https://gamma-api.polymarket.com/markets\"\n", "\n", "async def fetch_and_print_markets():\n", "    \"\"\"\n", "    Fetch all markets and print their titles/questions.\n", "    \"\"\"\n", "    async with httpx.AsyncClient() as client:\n", "        try:\n", "            print(\"Fetching markets from Polymarket API...\\n\")\n", "            \n", "            offset = 0\n", "            limit = 500\n", "            market_count = 0\n", "            \n", "            while True:\n", "                url = f\"{BASE_API_URL}?limit={limit}&offset={offset}&closed=false\"\n", "                print(f\"Fetching batch starting at offset {offset}...\")\n", "                \n", "                response = await client.get(url, timeout=10.0)\n", "                response.raise_for_status()\n", "                \n", "                batch = response.json()\n", "                if not batch:\n", "                    break\n", "                \n", "                print(f\"\\nMarkets {offset + 1} to {offset + len(batch)}:\")\n", "                print(\"-\" * 80)\n", "                \n", "                for market in batch:\n", "                    market_count += 1\n", "                    print(f\"{market_count}. {market['question']}\")\n", "                \n", "                if len(batch) < limit:\n", "                    break\n", "                    \n", "                offset += limit\n", "                print(\"\\n\")  # Add some spacing between batches\n", "            \n", "            print(f\"\\nTotal markets found: {market_count}\")\n", "            \n", "        except httpx.HTTPError as e:\n", "            print(f\"Error fetching markets: {e}\")\n", "        except Exception as e:\n", "            print(f\"Unexpected error: {e}\")\n", "            raise\n", "\n", "# Run directly in Jupyter\n", "await fetch_and_print_markets()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: GET https://gamma-api.polymarket.com/markets?limit=500&offset=0&closed=false \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: GET https://gamma-api.polymarket.com/markets?limit=500&offset=500&closed=false \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: GET https://gamma-api.polymarket.com/markets?limit=500&offset=1000&closed=false \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: GET https://gamma-api.polymarket.com/markets?limit=500&offset=1500&closed=false \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: GET https://gamma-api.polymarket.com/markets?limit=500&offset=2000&closed=false \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: GET https://gamma-api.polymarket.com/markets?limit=500&offset=2500&closed=false \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["'volume'\n", "{'question': 'Manchester City wins the Premier League?', 'market_slug': '506728', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 639860, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '8788519.42636101', 'hot': -88.23529411764706, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Arsenal wins the Premier League?', 'market_slug': '506729', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 640509, tzinfo=datetime.timezone.utc), 'odds': 0.025, 'volume': '5368050.30481999', 'hot': -84.07643312101911, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Aston Villa wins the Premier League?', 'market_slug': '506730', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 641098, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '30732816.852408', 'hot': -66.66666666666666, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Bournemouth wins the Premier League?', 'market_slug': '506731', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 641650, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '42349709.0107738', 'hot': -66.66666666666666, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Brentford wins the Premier League?', 'market_slug': '506732', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 645013, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '38302431.9910888', 'hot': -60.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Brighton & Hove Albion wins the Premier League?', 'market_slug': '506733', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 645667, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '65902822.9862214', 'hot': -85.71428571428571, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Chelsea wins the Premier League?', 'market_slug': '506734', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 646347, tzinfo=datetime.timezone.utc), 'odds': 0.004, 'volume': '12896733.6468339', 'hot': -90.00000000000001, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Crystal Palace wins the Premier League?', 'market_slug': '506735', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 646855, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '34297305.3689579', 'hot': -60.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Fulham wins the Premier League?', 'market_slug': '506737', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 647365, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '44764897.852378', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Newcastle United wins the Premier League?', 'market_slug': '506741', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 648060, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '19777998.347531', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Nottingham Forest wins the Premier League?', 'market_slug': '506742', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 648665, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '97229737.9484789', 'hot': -57.14285714285714, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Liverpool wins the Premier League?', 'market_slug': '506747', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 649206, tzinfo=datetime.timezone.utc), 'odds': 0.96, 'volume': '11922771.2479749', 'hot': 35.0210970464135, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Arsenal win the UEFA Champions League?', 'market_slug': '507285', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 649758, tzinfo=datetime.timezone.utc), 'odds': 0.1, 'volume': '1334826.986309', 'hot': -28.571428571428577, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Aston Villa win the UEFA Champions League?', 'market_slug': '507286', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 650304, tzinfo=datetime.timezone.utc), 'odds': 0.023, 'volume': '121236530.787794', 'hot': 14.999999999999996, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Atletico Madrid win the UEFA Champions League?', 'market_slug': '507288', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 654566, tzinfo=datetime.timezone.utc), 'odds': 0.038, 'volume': '26626919.4892182', 'hot': 5.555555555555561, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Barcelona win the UEFA Champions League?', 'market_slug': '507289', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 655129, tzinfo=datetime.timezone.utc), 'odds': 0.17, 'volume': '3040266.693764', 'hot': 70.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> Bayer Leverkusen win the UEFA Champions League?', 'market_slug': '507290', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 655695, tzinfo=datetime.timezone.utc), 'odds': 0.039, 'volume': '46143201.820467', 'hot': 21.874999999999996, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Bayern Munich win the UEFA Champions League?', 'market_slug': '507291', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 656228, tzinfo=datetime.timezone.utc), 'odds': 0.1, 'volume': '1768118.471819', 'hot': 11.111111111111121, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Benfica win the UEFA Champions League?', 'market_slug': '507292', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 656776, tzinfo=datetime.timezone.utc), 'odds': 0.01, 'volume': '35014704.1534716', 'hot': 100.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Borussia Dortmund win the UEFA Champions League?', 'market_slug': '507294', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 657349, tzinfo=datetime.timezone.utc), 'odds': 0.019, 'volume': '17546694.021212', 'hot': 0.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Club Brugge win the UEFA Champions League?', 'market_slug': '507296', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 657879, tzinfo=datetime.timezone.utc), 'odds': 0.006, 'volume': '20562496.253866', 'hot': 50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': ' <PERSON> Fe<PERSON>ord win the UEFA Champions League?', 'market_slug': '507298', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 658411, tzinfo=datetime.timezone.utc), 'odds': 0.007, 'volume': '22579312.2929471', 'hot': 133.33333333333331, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Inter Milan win the UEFA Champions League?', 'market_slug': '507300', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 658927, tzinfo=datetime.timezone.utc), 'odds': 0.07, 'volume': '79619410.2092948', 'hot': 37.25490196078434, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Lille win the UEFA Champions League?', 'market_slug': '507302', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 662748, tzinfo=datetime.timezone.utc), 'odds': 0.011, 'volume': '19193748.4460499', 'hot': 57.14285714285713, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Liverpool win the UEFA Champions League?', 'market_slug': '507303', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 663381, tzinfo=datetime.timezone.utc), 'odds': 0.16, 'volume': '7130063.10381802', 'hot': -10.112359550561791, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Paris Saint-Germain win the UEFA Champions League?', 'market_slug': '507305', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 664045, tzinfo=datetime.timezone.utc), 'odds': 0.063, 'volume': '44004895.2294721', 'hot': 162.5, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will PSV Eindhoven win the UEFA Champions League?', 'market_slug': '507306', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 664643, tzinfo=datetime.timezone.utc), 'odds': 0.006, 'volume': '13130056.1491709', 'hot': 50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Real Madrid win the UEFA Champions League?', 'market_slug': '507308', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 665187, tzinfo=datetime.timezone.utc), 'odds': 0.16, 'volume': '2801895.031772', 'hot': 23.076923076923077, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Real Madrid win La Liga?', 'market_slug': '507395', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 665749, tzinfo=datetime.timezone.utc), 'odds': 0.24, 'volume': '3606953.60404', 'hot': -51.02040816326531, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Barcelona win La Liga?', 'market_slug': '507396', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 666291, tzinfo=datetime.timezone.utc), 'odds': 0.55, 'volume': '3773634.60739504', 'hot': 96.42857142857143, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Atletico Madrid win La Liga?', 'market_slug': '507397', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 666829, tzinfo=datetime.timezone.utc), 'odds': 0.189, 'volume': '3517595.5593489', 'hot': -4.545454545454549, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Girona FC win La Liga?', 'market_slug': '507398', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 670784, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '21366499.935153', 'hot': -66.66666666666666, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Athletic Bilbao win La Liga?', 'market_slug': '507399', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 671447, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '14664562.4087081', 'hot': -40.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Real Sociedad win La Liga?', 'market_slug': '507400', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 672022, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '18354441.725735', 'hot': -75.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Real Betis win La Liga?', 'market_slug': '507401', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 672567, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '46851077.9413028', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Villarreal CF win La Liga?', 'market_slug': '507402', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 673090, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '8099656.54314602', 'hot': -66.66666666666666, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will RC Celta de Vigo win La Liga?', 'market_slug': '507403', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 673585, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '33152266.6214223', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Valencia CF win La Liga?', 'market_slug': '507404', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 674094, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '22363963.0546214', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Sevilla FC win La Liga?', 'market_slug': '507405', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 674699, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '23277795.9765018', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will another team win La Liga?', 'market_slug': '507413', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 675248, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '46049383.8061022', 'hot': -80.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON><PERSON><PERSON> be the top goalscorer in the EPL?', 'market_slug': '507666', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 679528, tzinfo=datetime.timezone.utc), 'odds': 0.115, 'volume': '185193.950869', 'hot': -55.769230769230774, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507675', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 680380, tzinfo=datetime.timezone.utc), 'odds': 0.823, 'volume': '288957.509369', 'hot': 44.385964912280706, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507676', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 681245, tzinfo=datetime.timezone.utc), 'odds': 0.025, 'volume': '254023.806742', 'hot': -63.76811594202899, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> <PERSON><PERSON> be the top goalscorer in the EPL?', 'market_slug': '507677', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 681967, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '434581.224984', 'hot': -60.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507678', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 682597, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '1527522.804863', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507679', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 683209, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '321767.464614', 'hot': 100.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507680', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 683801, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '416542.306847', 'hot': 50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507681', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 688797, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '352919.941931', 'hot': -97.95918367346938, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507682', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 689531, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '474504.779406', 'hot': 100.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507683', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 690208, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '790259.344304', 'hot': 100.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON><PERSON><PERSON> be the top goalscorer in the EPL?', 'market_slug': '507684', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 690771, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '388407.055607', 'hot': 50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON><PERSON> be the top goalscorer in the EPL?', 'market_slug': '507685', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 691339, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '327620.07628', 'hot': 100.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507686', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 691869, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '765542.604716', 'hot': -90.00000000000001, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> be the top goalscorer in the EPL?', 'market_slug': '507687', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 692454, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '437929.615266', 'hot': 0.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will another player be the top goalscorer in the EPL?', 'market_slug': '507688', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 697269, tzinfo=datetime.timezone.utc), 'odds': 0.007, 'volume': '458657.979501', 'hot': 16.666666666666664, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Atlanta Hawks win the 2025 NBA Finals?', 'market_slug': '507856', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 698109, tzinfo=datetime.timezone.utc), 'odds': 0.004, 'volume': '53272175.1568322', 'hot': -42.857142857142854, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Boston Celtics win the 2025 NBA Finals?', 'market_slug': '507857', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 698813, tzinfo=datetime.timezone.utc), 'odds': 0.27, 'volume': '3677749.12224998', 'hot': -3.571428571428574, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Brooklyn Nets win the 2025 NBA Finals?', 'market_slug': '507858', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 699406, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '41520445.512386', 'hot': -83.33333333333334, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Charlotte Hornets win the 2025 NBA Finals?', 'market_slug': '507859', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 699989, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '114408047.349832', 'hot': -85.71428571428571, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Chicago Bulls win the 2025 NBA Finals?', 'market_slug': '507860', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 700546, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '45425176.1262941', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Cleveland Cavaliers win the 2025 NBA Finals?', 'market_slug': '507861', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 703346, tzinfo=datetime.timezone.utc), 'odds': 0.124, 'volume': '4913006.389205', 'hot': 72.22222222222223, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Dallas Mavericks win the 2025 NBA Finals?', 'market_slug': '507862', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 704462, tzinfo=datetime.timezone.utc), 'odds': 0.015, 'volume': '2870548.139718', 'hot': -75.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Denver Nuggets win the 2025 NBA Finals?', 'market_slug': '507863', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 705486, tzinfo=datetime.timezone.utc), 'odds': 0.07, 'volume': '32609661.670178', 'hot': 75.00000000000001, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Detroit Pistons win the 2025 NBA Finals?', 'market_slug': '507864', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 706658, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '51283508.829013', 'hot': -57.14285714285714, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Golden State Warriors win the 2025 NBA Finals?', 'market_slug': '507865', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 707430, tzinfo=datetime.timezone.utc), 'odds': 0.047, 'volume': '5940601.960012', 'hot': 74.07407407407408, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Houston Rockets win the 2025 NBA Finals?', 'market_slug': '507866', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 708101, tzinfo=datetime.timezone.utc), 'odds': 0.009, 'volume': '16073930.377757', 'hot': -47.058823529411775, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Indiana Pacers win the 2025 NBA Finals?', 'market_slug': '507868', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 708673, tzinfo=datetime.timezone.utc), 'odds': 0.005, 'volume': '48332636.863167', 'hot': -28.57142857142857, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the LA Clippers win the 2025 NBA Finals?', 'market_slug': '507869', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 709216, tzinfo=datetime.timezone.utc), 'odds': 0.012, 'volume': '64872939.2543885', 'hot': 20.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Los Angeles Lakers win the 2025 NBA Finals?', 'market_slug': '507871', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 709888, tzinfo=datetime.timezone.utc), 'odds': 0.071, 'volume': '8388553.542851', 'hot': 208.69565217391303, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Memphis Grizzlies win the 2025 NBA Finals?', 'market_slug': '507872', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 712196, tzinfo=datetime.timezone.utc), 'odds': 0.015, 'volume': '3562461.79925899', 'hot': -48.275862068965516, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Miami Heat win the 2025 NBA Finals?', 'market_slug': '507873', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 712794, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '78325611.862653', 'hot': -62.5, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Milwaukee Bucks win the 2025 NBA Finals?', 'market_slug': '507875', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 713375, tzinfo=datetime.timezone.utc), 'odds': 0.018, 'volume': '3798946.43374801', 'hot': -55.00000000000001, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Minnesota Timberwolves win the 2025 NBA Finals?', 'market_slug': '507876', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 714857, tzinfo=datetime.timezone.utc), 'odds': 0.008, 'volume': '4502302.358554', 'hot': -66.66666666666666, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the New Orleans Pelicans win the 2025 NBA Finals?', 'market_slug': '507882', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 715489, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '56722527.889942', 'hot': -60.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the New York Knicks win the 2025 NBA Finals?', 'market_slug': '507883', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 716083, tzinfo=datetime.timezone.utc), 'odds': 0.04, 'volume': '971955.759611', 'hot': -33.33333333333333, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Oklahoma City Thunder win the 2025 NBA Finals?', 'market_slug': '507884', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 716632, tzinfo=datetime.timezone.utc), 'odds': 0.27, 'volume': '1969332.231782', 'hot': 42.10526315789475, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Orlando Magic win the 2025 NBA Finals?', 'market_slug': '507888', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 717195, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '8199312.690731', 'hot': -87.5, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Philadelphia 76ers win the 2025 NBA Finals?', 'market_slug': '507889', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 722981, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '5736611.002505', 'hot': -96.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': ' Will the Phoenix Suns win the 2025 NBA Finals?', 'market_slug': '507890', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 738143, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '6044541.657311', 'hot': -93.75, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Portland Trail Blazers win the 2025 NBA Finals?', 'market_slug': '507891', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 739641, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '39362318.090445', 'hot': -40.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Sacramento Kings win the 2025 NBA Finals?', 'market_slug': '507892', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 740450, tzinfo=datetime.timezone.utc), 'odds': 0.004, 'volume': '370086302.087923', 'hot': -55.55555555555555, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the San Antonio Spurs win the 2025 NBA Finals?', 'market_slug': '507893', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 741542, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '13796200.300574', 'hot': -62.5, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Toronto Raptors win the 2025 NBA Finals?', 'market_slug': '507894', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 742948, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '151408667.210432', 'hot': -75.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Utah Jazz win the 2025 NBA Finals?', 'market_slug': '507895', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 743565, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '125264538.105971', 'hot': -75.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will the Washington Wizards win the 2025 NBA Finals?', 'market_slug': '507896', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 743940, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '125925602.195164', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507906', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 744243, tzinfo=datetime.timezone.utc), 'odds': 0.2, 'volume': '347971.276428', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON><PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507911', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 744524, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '712890.477081', 'hot': -92.3076923076923, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507912', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 744819, tzinfo=datetime.timezone.utc), 'odds': 0.79, 'volume': '297765.213524', 'hot': 125.71428571428574, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> <PERSON><PERSON><PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507914', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 745106, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '333878.241962', 'hot': -98.21428571428571, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507915', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 745381, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '559937.246343', 'hot': -75.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> <PERSON><PERSON><PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507917', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 745671, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '607145.399898001', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> <PERSON><PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507918', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 745952, tzinfo=datetime.timezone.utc), 'odds': 0.003, 'volume': '441250.202113', 'hot': -93.33333333333333, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507919', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 746232, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '560218.591754', 'hot': -92.85714285714286, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON><PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507920', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 746511, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '1006809.8158', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507921', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 746786, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '1054681.252097', 'hot': -33.33333333333333, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507922', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 747061, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '410839.62091', 'hot': -66.66666666666666, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON><PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507923', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 747399, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '567451.555903', 'hot': -66.66666666666666, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507925', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 747826, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '376087.896391', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON> <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507927', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 748258, tzinfo=datetime.timezone.utc), 'odds': 0.002, 'volume': '397072.895903', 'hot': 100.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will <PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507929', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 748598, tzinfo=datetime.timezone.utc), 'odds': 0.001, 'volume': '501694.843487', 'hot': -50.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': '<PERSON><PERSON> win the 2024-25 NBA MVP?', 'market_slug': '507931', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 748947, tzinfo=datetime.timezone.utc), 'odds': 0.004, 'volume': '752266.455804', 'hot': 100.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Ajax win the UEFA Europa League?', 'market_slug': '507977', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 749257, tzinfo=datetime.timezone.utc), 'odds': 0.039, 'volume': '97608.197761', 'hot': -13.33333333333333, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Athletic Bilbao win the UEFA Europa League?', 'market_slug': '507979', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 749589, tzinfo=datetime.timezone.utc), 'odds': 0.14, 'volume': '61580.86305', 'hot': 0.0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will AZ Alkmaar win the UEFA Europa League?', 'market_slug': '507980', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 9, 58, 749931, tzinfo=datetime.timezone.utc), 'odds': 0.02, 'volume': '810455.509114', 'hot': 400.0, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.445927 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.813070 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'question': 'Will Fenerbahce win the UEFA Europa League?', 'market_slug': '507987', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 2, 93097, tzinfo=datetime.timezone.utc), 'odds': 0.05, 'volume': '96734.0706379999', 'hot': 0, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.486900 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.758487 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'question': 'Will Frankfurt win the UEFA Europa League?', 'market_slug': '507989', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 4, 401831, tzinfo=datetime.timezone.utc), 'odds': 0.055, 'volume': '295694.542679', 'hot': 0, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.480819 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.786211 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'question': 'Will Lazio win the UEFA Europa League?', 'market_slug': '507992', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 6, 354646, tzinfo=datetime.timezone.utc), 'odds': 0.135, 'volume': '544624.393802', 'hot': 0, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.384605 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.953853 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'question': 'Will Lyon win the UEFA Europa League?', 'market_slug': '507994', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 8, 705330, tzinfo=datetime.timezone.utc), 'odds': 0.057, 'volume': '149642.626346', 'hot': 0, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.481432 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.908699 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'question': 'Will Manchester United win the UEFA Europa League?', 'market_slug': '507997', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 11, 97529, tzinfo=datetime.timezone.utc), 'odds': 0.13, 'volume': '97282.564455', 'hot': 0, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Olympiacos win the UEFA Europa League?', 'market_slug': '508000', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 11, 98981, tzinfo=datetime.timezone.utc), 'odds': 0.042, 'volume': '255635.595497', 'hot': 50.000000000000014, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Rangers win the UEFA Europa League?', 'market_slug': '508004', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 11, 99748, tzinfo=datetime.timezone.utc), 'odds': 0.017, 'volume': '403046.951496', 'hot': 112.5, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Real Sociedad win the UEFA Europa League?', 'market_slug': '508005', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 11, 100352, tzinfo=datetime.timezone.utc), 'odds': 0.079, 'volume': '44974.912881', 'hot': -1.250000000000001, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Roma win the UEFA Europa League?', 'market_slug': '508007', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 11, 104831, tzinfo=datetime.timezone.utc), 'odds': 0.056, 'volume': '93233.6127850026', 'hot': 27.27272727272728, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Tottenham Hotspur win the UEFA Europa League?', 'market_slug': '508009', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 11, 105686, tzinfo=datetime.timezone.utc), 'odds': 0.17, 'volume': '92825.672121', 'hot': 30.769230769230777, 'imp': 0.0, 'classification': 'N/A'}\n", "{'question': 'Will Another team win the UEFA Europa League?', 'market_slug': '508062', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 11, 106392, tzinfo=datetime.timezone.utc), 'odds': 0.026, 'volume': '90084.715401', 'hot': -45.833333333333336, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.412598 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.773159 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'question': 'Will Liverpool finish in the top 4 of EPL?', 'market_slug': '508120', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 13, 319790, tzinfo=datetime.timezone.utc), 'odds': 0.991, 'volume': '40049.604601', 'hot': 0, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.395607 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.890676 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'question': 'Will Manchester United finish in the top 4 of EPL?', 'market_slug': '508143', 'volatility': 0.0, 'volatility_last_24_hours': 0.0, 'lastUpdated': datetime.datetime(2025, 3, 2, 22, 10, 15, 572413, tzinfo=datetime.timezone.utc), 'odds': 0.01, 'volume': '69634.926875', 'hot': 0, 'imp': 0.0, 'classification': 'N/A'}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.423280 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 0.755328 seconds\n", "INFO:httpx:HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n"]}], "source": ["import httpx\n", "import asyncio\n", "import nest_asyncio\n", "import pandas as pd\n", "from datetime import datetime, timezone\n", "from statistics import stdev\n", "from openai import AsyncOpenAI\n", "import motor \n", "import random\n", "import math\n", "import json\n", "\n", "nest_asyncio.apply()\n", "\n", "MONGO_CONN = motor.motor_asyncio.AsyncIOMotorClient(\"mongodb://localhost:27017/\")\n", "db = MONGO_CONN.vol_db  # Export this db object\n", "markets_collection = db[\"markets\"]\n", "\n", "BASE_API_URL = \"https://gamma-api.polymarket.com/markets\"\n", "POLYMARKET_API = \"https://gamma-api.polymarket.com/events?limit=500&closed=false\"\n", "OPENAI_CLIENT = AsyncOpenAI(api_key=\"********************************************************************************************************************************************************************\")\n", "\n", "async def fetch_markets():\n", "    \"\"\"\n", "    Fetch all markets from Polymarket API and return them as a list.\n", "    \"\"\"\n", "    async with httpx.AsyncClient() as client:\n", "        markets = []\n", "        offset = 0\n", "        limit = 500\n", "\n", "        while True:\n", "            url = f\"{BASE_API_URL}?limit={limit}&offset={offset}&closed=false\"\n", "            response = await client.get(url, timeout=10.0)\n", "            response.raise_for_status()\n", "            batch = response.json()\n", "            \n", "            if not batch:\n", "                break\n", "\n", "            markets.extend(batch)\n", "            if len(batch) < limit:\n", "                break\n", "            \n", "            offset += limit\n", "\n", "        return markets\n", "\n", "async def classify_news_story(story):\n", "    categories = [\"Politics\", \"Crypto\", \"Business\", \"Culture\", \"Technology\", \"Sports\", \"Other\"]\n", "    prompt = f\"Read the following news story and assign one of these categories based on the content: {', '.join(categories)}\\n\\nStory: {story} return your response in json like this\" + \"{'answer': 'category'}\"\n", "   \n", "    chat_completion = await OPENAI_CLIENT.chat.completions.create(\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a knowledgeable assistant helping to classify news stories into categories.\"},\n", "            {\"role\": \"user\", \"content\": prompt}\n", "        ],\n", "        model=\"gpt-3.5-turbo\",\n", "        response_format={\"type\": \"json_object\"}\n", "    )\n", "   \n", "    return json.loads(chat_completion.choices[0].message.content)\n", "\n", "async def refresh_markets():\n", "    \"\"\"\n", "    Fetch and process active markets.\n", "    \"\"\"\n", "    active_markets = await fetch_markets()\n", "    if not active_markets:\n", "        print(\"No active markets found.\")\n", "        return\n", "\n", "    for market in active_markets:\n", "        try:\n", "            market_slug = market[\"id\"]\n", "            past_data = await markets_collection.find_one({\"market_slug\": market_slug})\n", "            past_prices = past_data.get(\"prices\", []) if past_data else []\n", "\n", "            current_price = market['lastTradePrice']\n", "            if current_price:\n", "                past_prices.append(current_price)\n", "                \n", "            volatility = stdev(past_prices[-24:]) if len(past_prices) >= 24 else 0.0\n", "            volatility_last_24_hours = stdev(past_prices[-288:]) if len(past_prices) >= 288 else 0.0\n", "            percentage_change = ((current_price - past_prices[-2]) / past_prices[-2]) * 100 if len(past_prices) >= 2 else 0\n", "            imp = volatility * float(market['volume']) if market['volume'] else 0\n", "            classification = past_data['classification'] if past_data else \"N/A\"\n", "\n", "            if past_data is None:\n", "                try:\n", "                    clf = await classify_news_story(market['question'])\n", "                    classification = clf['answer']\n", "\n", "                except:\n", "                    classification = \"N/A\"\n", "            \n", "            else:\n", "                try:\n", "                    classification = market['classification']\n", "\n", "                except:\n", "                    classification = \"N/A\"\n", "\n", "            markets_collection.update_one(\n", "                {\"market_slug\": market_slug},\n", "                {\n", "                    \"$set\": {\n", "                        \"question\": market[\"question\"],\n", "                        \"market_slug\": market_slug,\n", "                        \"volatility\": volatility,\n", "                        \"volatility_last_24_hours\": volatility_last_24_hours,\n", "                        \"lastUpdated\": datetime.now(timezone.utc),\n", "                        \"odds\": current_price,\n", "                        \"volume\": market['volume'],\n", "                        \"hot\": percentage_change,\n", "                        \"imp\": imp,\n", "                        \"classification\": classification,\n", "                    },\n", "                    \"$push\": {\"prices\": {\"$each\": [current_price], \"$slice\": -288}},\n", "                },\n", "                upsert=True\n", "            )\n", "        except Exception as e:\n", "            print(e)\n", "            pass\n", "\n", "    print(f\"Refreshed {len(active_markets)} markets.\")\n", "\n", "# Run the market refresh function\n", "await refresh_markets()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import httpx\n", "import pandas as pd\n", "from datetime import datetime, timezone\n", "from mongo_manager import markets_collection\n", "from statistics import stdev\n", "import pandas as pd\n", "from openai import AsyncOpenAI\n", "import random \n", "import math \n", "import json \n", "\n", "POLYMARKET_API = \"https://gamma-api.polymarket.com/events?limit=500&closed=false\"\n", "OPENAI_CLIENT = AsyncOpenAI(api_key=\"********************************************************************************************************************************************************************\")\n", "\n", "\n", "async def classify_news_story(story):\n", "    categories = [\"Politics\", \"Crypto\", \"Business\", \"Culture\", \"Technology\", \"Sports\", \"Other\"]\n", "    prompt = f\"Read the following news story and assign one of these categories based on the content: {', '.join(categories)}\\n\\nStory: {story} return your response in json like this\" + \"{'answer': 'category'}\"\n", "   \n", "    chat_completion = await OPENAI_CLIENT.chat.completions.create(\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a knowledgeable assistant helping to classify news stories into categories.\"},\n", "            {\"role\": \"user\", \"content\": prompt}\n", "        ],\n", "        model=\"gpt-3.5-turbo\",\n", "        response_format={\n", "            \"type\": \"json_object\"\n", "        }\n", "    )\n", "   \n", "    return json.loads(chat_completion.choices[0].message.content)\n", "\n", "async def fetch_active_markets():\n", "    \"\"\"\n", "    Fetch active markets from the Polymarket API using httpx.\n", "    \"\"\"\n", "    async with httpx.AsyncClient() as client:\n", "        try:\n", "            response = await client.get(POLYMARKET_API, timeout=10.0)\n", "            response.raise_for_status()\n", "            markets = []\n", "\n", "            for comp in response.json():\n", "                for mkt in comp['markets']:\n", "                    # if mkt['acceptingOrders']:\n", "                    if mkt['closed']:\n", "                        continue \n", "\n", "                    else:\n", "                        try:\n", "                            if mkt['lastTradePrice'] > 0 and mkt['lastTradePrice'] < 1:\n", "                                mkt['slg'] = comp['slug']\n", "                                markets.append(mkt)\n", "\n", "                            else:\n", "                                continue \n", "\n", "                        except Exception as e:\n", "                            None\n", "\n", "            return markets \n", "            \n", "        except httpx.HTTPError as e:\n", "            print(f\"Error fetching markets: {e}\")\n", "            return []\n", "\n", "def calculate_volatility(prices):\n", "    if len(prices) < 2:\n", "        return 0.0  # Not enough data points to calculate volatility\n", "    \n", "    # Step 1: Find the average price\n", "    avg_price = sum(prices) / len(prices)\n", "\n", "    # Step 2: Calculate the squared differences from the average price\n", "    squared_diffs = [(price - avg_price) ** 2 for price in prices]\n", "\n", "    # Step 3: Calculate the variance (mean of squared differences)\n", "    variance = sum(squared_diffs) / len(prices)\n", "\n", "    # Step 4: Calculate the standard deviation (square root of variance)\n", "    volatility = math.sqrt(variance)\n", "    \n", "    return volatility\n", "\n", "def format_number(num):\n", "    \"\"\"Format number to keep only the decimal part and remove trailing zeros.\"\"\"\n", "    decimal_part = f\"{num:.3f}\"[f\"{num:.3f}\".index(\".\"):]\n", "    return decimal_part.rstrip('0')  # Remove trailing zeros\n", "\n", "def calculate_odds_trend(prices):\n", "    if len(prices) < 2:\n", "        return ''\n", "    \n", "    mid_index = len(prices) // 2\n", "    \n", "    # Step 1: Calculate the averages of the first half and the second half\n", "    first_half = prices[:mid_index]\n", "    second_half = prices[mid_index:]\n", "    \n", "    avg_first_half = round(sum(first_half) / len(first_half), 3)\n", "    avg_second_half = round(sum(second_half) / len(second_half), 3)\n", "    \n", "    # Step 2: Determine if the odds are going up, down, or staying the same\n", "    if avg_first_half == avg_second_half:\n", "        start_price = round(prices[0], 3)\n", "        end_price = round(prices[-1], 3)\n", "        \n", "        if start_price > end_price:\n", "            return f\"{format_number(start_price)} 📉 {format_number(end_price)}\"  \n", "        elif start_price < end_price:\n", "            return f\"{format_number(start_price)} 📈 {format_number(end_price)}\"\n", "        return f\"{format_number(start_price)} ➡️ {format_number(end_price)}\"\n", "    \n", "    if avg_first_half < avg_second_half:\n", "        # Ascending trend\n", "        min_first = min(first_half)\n", "        max_second = max(second_half)\n", "        return f\"{format_number(min_first)} 📈 {format_number(max_second)}\"\n", "    else:\n", "        # Descending trend\n", "        max_first = max(first_half)\n", "        min_second = min(second_half)\n", "        return f\"{format_number(max_first)} 📉 {format_number(min_second)}\"\n", "\n", "\n", "async def refresh_markets():\n", "    \"\"\"\n", "    Refresh active markets in the database and add volatility.\n", "    \"\"\"\n", "    active_markets = await fetch_active_markets()\n", "    if not active_markets:\n", "        print(\"No active markets found.\")\n", "        return\n", "\n", "    for market in active_markets:\n", "        try:\n", "            market_slug = market[\"id\"]\n", "\n", "            # Fetch past prices from the database for volatility calculation\n", "            past_data = await markets_collection.find_one({\"market_slug\": market_slug})\n", "            past_prices = past_data.get(\"prices\", []) if past_data else []\n", "\n", "            # Simulated price (replace with actual API or data source for market prices)\n", "            current_price = market['lastTradePrice']\n", "            # current_price = random.uniform(10, 500)  # Random price between 10 and 500\n", "\n", "            if current_price:\n", "                past_prices.append(current_price)\n", "                # Keep only the last 24 hours of data\n", "                # past_prices = past_prices[-24:]\n", "\n", "\n", "            # Calculate 24-hour and 2-hour volatility\n", "            volatility = calculate_volatility(past_prices[-24:])\n", "            volatility_last_24_hours = calculate_volatility(past_prices[-288:])\n", "            # volatility = calculate_volatility(past_prices[-24:]) if len(past_prices) >= 24 else 0\n", "            # volatility_last_24_hours = calculate_volatility(past_prices[-288:]) if len(past_prices) >= 288 else 0\n", "            odds_trend = calculate_odds_trend(past_prices[-288:])\n", "            # print(f\"new volatility for last 24 hours for {market_slug} is {volatility}\")\n", "\n", "            old_record = None \n", "            old_price = None \n", "            \n", "            try:\n", "                old_record = await markets_collection.find_one({\"market_slug\": market_slug})\n", "                old_price = old_record['odds']\n", "\n", "            except:\n", "                pass \n", "            \n", "            percentage_change = ((current_price - old_price) / old_price) * 100 if old_price else 0\n", "            # try:\n", "            #     percentage_change = ((current_price - old_price) / old_price) * 100\n", "\n", "            # except:\n", "            #     percentage_change = 0\n", "\n", "            imp = volatility * float(market['volume']) if market['volume'] else 0\n", "\n", "            # Update or insert the market into the database\n", "            classification = None \n", "\n", "            if old_record is None:\n", "                print(\"right here.....\")\n", "                \n", "                try:\n", "                    clf = await classify_news_story(market['question'])\n", "                    market['classification'] = clf['answer']\n", "                    classification = clf['answer']\n", "                \n", "                except:\n", "                    classification = \"N/A\"\n", "                \n", "            else:\n", "                try:\n", "                    classification = old_record['classification']\n", "\n", "                except:\n", "                    classification = \"N/A\"\n", "            \n", "            markets_collection.update_one(\n", "                {\"market_slug\": market_slug},\n", "                {\n", "                    \"$set\": {\n", "                        \"question\": market[\"question\"],\n", "                        \"market_slug\": market_slug,\n", "                        \"volatility\": volatility,\n", "                        \"volatility_last_24_hours\": volatility_last_24_hours,\n", "                        \"icon\": market.get(\"icon\"),\n", "                        \"lastUpdated\": datetime.now(timezone.utc),\n", "                        \"odds\": current_price,\n", "                        \"odds_trend\" : odds_trend,\n", "                        \"volume\": market['volume'],\n", "                        \"slg\": market['slg'],\n", "                        \"hot\": percentage_change,\n", "                        \"imp\": imp,\n", "                        \"classification\": classification,\n", "                        \"spread\": market['spread']\n", "                    },\n", "                    \"$push\": {\"prices\": {\"$each\": [current_price], \"$slice\": -288}},  # Keep the latest 24 prices\n", "                },\n", "                upsert=True\n", "            )\n", "\n", "        except Exception as e:\n", "            print(e)\n", "            pass \n", "\n", "    print(f\"Refreshed {len(active_markets)} markets.\")\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}