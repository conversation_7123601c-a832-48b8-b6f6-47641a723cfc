import logging
import motor.motor_asyncio
from pymongo.errors import ConnectionFailure, OperationFailure, ServerSelectionTimeoutError
import os
import json
import time
from datetime import datetime
import asyncio
from dotenv import load_dotenv
import os

# Load environment variables from .env
load_dotenv()


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add backup directory configuration
BACKUP_DIR = os.path.join(os.path.dirname(__file__), "db_backups")
os.makedirs(BACKUP_DIR, exist_ok=True)


MONGO_USERNAME = os.getenv("MONGO_USERNAME")    # Your MongoDB username
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")    # Your MongoDB password
MONGO_HOST = os.getenv("MONGO_HOST")            # MongoDB host
MONGO_PORT = int(os.getenv("MONGO_PORT"))       # MongoDB port (default is 27017)
MONGO_AUTH_DB = os.getenv("MONGO_AUTH_DB")      # Authentication database is now "vol_db"
         
print(MONGO_USERNAME, MONGO_PASSWORD, MONGO_HOST, MONGO_PORT, MONGO_AUTH_DB)

# MongoDB connection URI with correct authentication
#MONGO_URI = f"mongodb://{MONGO_USERNAME}:{MONGO_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/{MONGO_AUTH_DB}?authSource={MONGO_AUTH_DB}"
MONGO_URI = f"mongodb://{MONGO_USERNAME}:{MONGO_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/{MONGO_AUTH_DB}?authSource=admin"

# Initialize the connection and database       sxfsxfrctdsgfjt
try:
    MONGO_CONN = motor.motor_asyncio.AsyncIOMotorClient(
        MONGO_URI,
        serverSelectionTimeoutMS=5000,    # 5 seconds timeout for server selection
        connectTimeoutMS=10000,           # 10 seconds timeout for initial connection
        socketTimeoutMS=45000,            # 45 seconds timeout for operations
        maxPoolSize=10,                   # Connection pool size
        retryWrites=True,                 # Retry write operations
        w="majority"                      # Write concern - write to majority
    )
    db = MONGO_CONN[MONGO_AUTH_DB]  # Your application database
    markets_collection = db["markets"]
    classification_queue = db["classification_queue"]
    logger.info("Successfully connected to MongoDB.")
except ConnectionFailure as e:
    logger.error(f"MongoDB connection failure: {e}")
    db = None
except OperationFailure as e:
    logger.error(f"Authentication failed: {e}")
    db = None
except Exception as e:
    logger.error(f"An unexpected error occurred: {e}")
    db = None

async def validate_connection():
    """Check if the MongoDB connection is valid and reconnect if needed"""
    global MONGO_CONN, db, markets_collection, classification_queue
    
    try:
        # Check if connection is alive by making a lightweight call
        await MONGO_CONN.admin.command('ping')
        logger.info("MongoDB connection validated successfully")
        
        # Check collection existence
        collection_validation = await validate_collections()
        if not collection_validation:
            logger.warning("MongoDB collections validation failed despite successful connection")
            # Collections might be missing, but connection is fine
            # Continue with reconnection to ensure collections are initialized
        else:
            return True
            
    except Exception as e:
        logger.error(f"MongoDB connection validation failed: {e}")
    
    # Reconnect
    try:
        logger.info("Attempting to reconnect to MongoDB...")
        MONGO_CONN = motor.motor_asyncio.AsyncIOMotorClient(
            MONGO_URI,
            serverSelectionTimeoutMS=5000,
            connectTimeoutMS=10000,
            socketTimeoutMS=45000,
            maxPoolSize=10,
            retryWrites=True,
            w="majority"
        )
        
        # Verify new connection
        await MONGO_CONN.admin.command('ping')
        
        # Reconnect collections
        db = MONGO_CONN[MONGO_AUTH_DB]
        markets_collection = db["markets"]
        classification_queue = db["classification_queue"]
        
        # Validate collections
        collection_validation = await validate_collections()
        if not collection_validation:
            logger.warning("Collection validation failed after reconnection")
            # We'll continue anyway since we at least have a connection
            
        logger.info("Successfully reconnected to MongoDB")
        return True
    except Exception as reconnect_error:
        logger.error(f"Reconnection failed: {reconnect_error}")
        return False

async def validate_collections():
    """Verify that the collections exist and contain expected data"""
    try:
        # Check if collections exist
        collections = await db.list_collection_names()
        expected_collections = ["markets", "classification_queue"]
        missing_collections = [c for c in expected_collections if c not in collections]
        
        if missing_collections:
            logger.warning(f"Missing collections: {missing_collections}")
            # Create missing collections if needed
            for collection_name in missing_collections:
                await db.create_collection(collection_name)
                logger.info(f"Created missing collection: {collection_name}")
        
        # Verify markets collection has data
        market_count = await markets_collection.count_documents({})
        logger.info(f"Markets collection contains {market_count} documents")
        
        # Check for index on market_slug
        existing_indexes = await markets_collection.index_information()
        if "market_slug_1" not in existing_indexes:
            logger.info("Creating index on market_slug field")
            await markets_collection.create_index("market_slug")
        
        # Check for index on question field
        if "question_1" not in existing_indexes:
            logger.info("Creating index on question field")
            await markets_collection.create_index("question")
            
        return True
    except Exception as e:
        logger.error(f"Error validating collections: {e}")
        return False

async def repair_database():
    """
    Attempt to repair database issues:
    1. Fix any markets with null classifications
    2. Remove duplicate markets
    3. Ensure consistency between markets and queue
    """
    try:
        # Fix null classifications
        null_result = await markets_collection.update_many(
            {"classification": None},
            {"$set": {"classification": "N/A"}}
        )
        logger.info(f"Fixed {null_result.modified_count} markets with null classification")
        
        # Check for duplicate markets by market_slug
        pipeline = [
            {"$group": {"_id": "$market_slug", "count": {"$sum": 1}}},
            {"$match": {"count": {"$gt": 1}}}
        ]
        duplicates = await markets_collection.aggregate(pipeline).to_list(length=None)
        
        for dup in duplicates:
            market_slug = dup["_id"]
            logger.warning(f"Found {dup['count']} duplicates for market_slug: {market_slug}")
            
            # Keep the most recently updated document and remove others
            latest = await markets_collection.find({"market_slug": market_slug}).sort("lastUpdated", -1).limit(1).to_list(1)
            
            if latest:
                latest_id = latest[0]["_id"]
                delete_result = await markets_collection.delete_many({
                    "market_slug": market_slug,
                    "_id": {"$ne": latest_id}
                })
                logger.info(f"Removed {delete_result.deleted_count} duplicate markets for {market_slug}")
                
        # Check queue integrity
        queue_items = await classification_queue.find({}).to_list(None)
        orphaned = 0
        
        for item in queue_items:
            market_slug = item.get("market_slug")
            market = await markets_collection.find_one({"market_slug": market_slug})
            
            if not market:
                # Remove queue item as the market doesn't exist
                await classification_queue.delete_one({"_id": item["_id"]})
                orphaned += 1
                
        if orphaned > 0:
            logger.info(f"Removed {orphaned} orphaned queue items")
            
        return True
    except Exception as e:
        logger.error(f"Error repairing database: {e}")
        return False

async def cleanup_old_backups(max_backups=5):
    """
    Remove old backup files keeping only the most recent ones.
    
    Args:
        max_backups: Maximum number of backup files to keep
    """
    try:
        if not os.path.exists(BACKUP_DIR):
            return
            
        backup_files = [os.path.join(BACKUP_DIR, f) for f in os.listdir(BACKUP_DIR) 
                       if f.startswith("markets_backup_") and f.endswith(".json")]
        
        if len(backup_files) <= max_backups:
            return
            
        # Sort by modification time (oldest first)
        backup_files.sort(key=os.path.getmtime)
        
        # Calculate how many to remove
        files_to_remove = backup_files[:-max_backups]
        total_size = sum(os.path.getsize(f) for f in files_to_remove) / (1024 * 1024)
        
        logger.info(f"Cleaning up {len(files_to_remove)} old backups to free {total_size:.2f} MB")
        
        # Remove old backups
        for old_file in files_to_remove:
            try:
                os.remove(old_file)
                logger.info(f"Removed old backup: {os.path.basename(old_file)}")
            except Exception as e:
                logger.error(f"Error removing old backup {old_file}: {e}")
                
        return True
    except Exception as e:
        logger.error(f"Error cleaning up old backups: {e}")
        return False

def get_free_disk_space_mb(path):
    """Get free disk space in MB for the given path."""
    try:
        if os.name == 'nt':  # Windows
            import ctypes
            free_bytes = ctypes.c_ulonglong(0)
            ctypes.windll.kernel32.GetDiskFreeSpaceExW(ctypes.c_wchar_p(path), None, None, ctypes.pointer(free_bytes))
            return free_bytes.value / (1024 * 1024)
        else:  # Unix/Linux/MacOS
            stats = os.statvfs(path)
            return (stats.f_frsize * stats.f_bavail) / (1024 * 1024)
    except Exception as e:
        logger.error(f"Error getting free disk space: {e}")
        return 10000  # Default to 10GB as fallback

async def create_database_backup(max_docs=10000, max_backups=5):
    """
    Create a backup of the current database state.
    
    Args:
        max_docs: Maximum number of documents to backup
        max_backups: Maximum number of backup files to keep
    
    Returns:
        str: Path to the backup file or None if backup failed
    """
    try:
        # Clean up old backups first to ensure we don't waste disk space
        await cleanup_old_backups(max_backups)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(BACKUP_DIR, f"markets_backup_{timestamp}.json")
        
        # Fetch data from collections
        markets_data = await markets_collection.find().limit(max_docs).to_list(length=max_docs)
        queue_data = await classification_queue.find().to_list(length=max_docs)
        
        # Custom JSON encoder to handle MongoDB data types
        class MongoJSONEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, datetime):
                    return {"$date": obj.isoformat()}
                if hasattr(obj, '__str__'):
                    return str(obj)
                return super().default(obj)
        
        # Process documents to make them JSON serializable
        def process_doc(doc):
            processed = {}
            for key, value in doc.items():
                if key == '_id':
                    processed[key] = str(value)
                elif isinstance(value, dict):
                    processed[key] = process_doc(value)
                elif isinstance(value, list):
                    processed[key] = [process_doc(item) if isinstance(item, dict) else item for item in value]
                else:
                    processed[key] = value
            return processed
        
        # Convert documents to JSON-serializable format
        markets_data = [process_doc(doc) for doc in markets_data]
        queue_data = [process_doc(doc) for doc in queue_data]
        
        # Create backup object
        backup = {
            "timestamp": timestamp,
            "markets_count": len(markets_data),
            "queue_count": len(queue_data),
            "markets": markets_data,
            "queue": queue_data
        }
        
        # Calculate approximate size before writing
        approx_size_mb = len(json.dumps(backup, cls=MongoJSONEncoder)) / (1024 * 1024)
        logger.info(f"Estimated backup size: {approx_size_mb:.2f} MB")
        
        # Check if we have enough disk space (10x safety margin)
        if approx_size_mb * 10 > get_free_disk_space_mb(BACKUP_DIR):
            logger.error(f"Not enough disk space for backup. Need ~{approx_size_mb * 10:.2f} MB")
            return None
        
        # Save to file
        with open(backup_file, 'w') as f:
            json.dump(backup, f, cls=MongoJSONEncoder, indent=2)
            
        file_size_mb = os.path.getsize(backup_file) / (1024 * 1024)
        logger.info(f"Created database backup at {backup_file} with {len(markets_data)} markets and {len(queue_data)} queue items ({file_size_mb:.2f} MB)")
        return backup_file
    except Exception as e:
        logger.error(f"Error creating database backup: {e}")
        return None

async def restore_from_backup(backup_file=None, skip_recent=0):
    """
    Restore the database from a backup file.
    
    Args:
        backup_file: Path to the backup file. If None, uses the latest backup.
        skip_recent: Number of most recent backups to skip (useful for emergency restoration)
        
    Returns:
        bool: True if restore was successful, False otherwise
    """
    try:
        # Find appropriate backup if no file specified
        if not backup_file:
            backup_files = [f for f in os.listdir(BACKUP_DIR) if f.startswith("markets_backup_") and f.endswith(".json")]
            if not backup_files:
                logger.error("No backup files found")
                return False
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda f: os.path.getmtime(os.path.join(BACKUP_DIR, f)), reverse=True)
            
            # Skip the N most recent backups if requested (for emergency restoration)
            if skip_recent > 0 and len(backup_files) > skip_recent:
                logger.info(f"Skipping {skip_recent} most recent backups for emergency restoration")
                selected_backup = backup_files[skip_recent]
                backup_file = os.path.join(BACKUP_DIR, selected_backup)
                logger.info(f"Selected backup file: {selected_backup} (created {datetime.fromtimestamp(os.path.getmtime(backup_file))})")
            else:
                # Use the most recent backup
                backup_file = os.path.join(BACKUP_DIR, backup_files[0])
        
        logger.info(f"Restoring from backup: {backup_file}")
        
        # Verify the backup file exists and has reasonable size
        if not os.path.exists(backup_file):
            logger.error(f"Backup file does not exist: {backup_file}")
            return False
            
        file_size_mb = os.path.getsize(backup_file) / (1024 * 1024)
        if file_size_mb < 0.1:  # Less than 100KB is suspicious
            logger.error(f"Backup file is suspiciously small ({file_size_mb:.2f} MB): {backup_file}")
            return False
            
        # Load backup data with error handling
        try:
            with open(backup_file, 'r') as f:
                backup = json.load(f)
                
            # Validate backup content
            if 'markets' not in backup or 'queue' not in backup:
                logger.error(f"Invalid backup format in {backup_file} - missing required sections")
                return False
                
            if len(backup.get('markets', [])) < 10 and len(backup.get('queue', [])) < 1:
                logger.error(f"Backup appears empty or corrupted: {len(backup.get('markets', []))} markets")
                return False
                
            logger.info(f"Backup validation passed: {len(backup.get('markets', []))} markets, {len(backup.get('queue', []))} queue items")
        except json.JSONDecodeError:
            logger.error(f"Failed to parse backup file (invalid JSON): {backup_file}")
            return False
        except Exception as load_error:
            logger.error(f"Error loading backup file {backup_file}: {load_error}")
            return False
        
        # Backup existing data before restore (use a different name pattern to avoid confusion)
        pre_restore_backup = os.path.join(BACKUP_DIR, f"pre_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        try:
            # Use a different function to avoid triggering cleanup logic during emergency
            await _create_emergency_backup(pre_restore_backup)
            logger.info(f"Created safety backup of current state at: {pre_restore_backup}")
        except Exception as backup_error:
            logger.error(f"Failed to create safety backup before restore: {backup_error}")
            # Continue anyway since we're already in an emergency restore
        
        # Get document counts before restoration
        markets_before = await markets_collection.count_documents({})
        queue_before = await classification_queue.count_documents({})
        logger.info(f"Current database state: {markets_before} markets, {queue_before} queue items")
        
        # Clear existing collections with error handling
        try:
            delete_markets_result = await markets_collection.delete_many({})
            delete_queue_result = await classification_queue.delete_many({})
            logger.info(f"Cleared existing collections: {delete_markets_result.deleted_count} markets, {delete_queue_result.deleted_count} queue items")
        except Exception as delete_error:
            logger.error(f"Error clearing collections: {delete_error}")
            return False
        
        # Restore markets
        markets_restored = 0
        if backup["markets"]:
            # Insert in smaller batches to avoid memory issues
            batch_size = 100
            for i in range(0, len(backup["markets"]), batch_size):
                batch = backup["markets"][i:i+batch_size]
                try:
                    insert_result = await markets_collection.insert_many(batch)
                    markets_restored += len(insert_result.inserted_ids)
                    logger.info(f"Restored batch of {len(batch)} markets (total: {markets_restored})")
                except Exception as insert_error:
                    logger.error(f"Error inserting market batch: {insert_error}")
                    # Continue with next batch
        
        # Restore queue
        queue_restored = 0
        if backup["queue"]:
            # Insert in smaller batches to avoid memory issues
            batch_size = 100
            for i in range(0, len(backup["queue"]), batch_size):
                batch = backup["queue"][i:i+batch_size]
                try:
                    insert_result = await classification_queue.insert_many(batch)
                    queue_restored += len(insert_result.inserted_ids)
                    logger.info(f"Restored batch of {len(batch)} queue items (total: {queue_restored})")
                except Exception as insert_error:
                    logger.error(f"Error inserting queue batch: {insert_error}")
                    # Continue with next batch
        
        # Verify restoration was successful
        markets_after = await markets_collection.count_documents({})
        queue_after = await classification_queue.count_documents({})
        
        logger.info(f"Restore complete. Database state change:")
        logger.info(f"  Markets: {markets_before} → {markets_after} ({markets_after - markets_before:+d})")
        logger.info(f"  Queue: {queue_before} → {queue_after} ({queue_after - queue_before:+d})")
        
        # Consider restoration successful if we restored some data and didn't lose data
        restoration_successful = (markets_after >= markets_before or markets_after > 10) and markets_restored > 0
        if restoration_successful:
            logger.info(f"RESTORATION SUCCESSFUL: Database now has {markets_after} markets and {queue_after} queue items")
        else:
            logger.error(f"RESTORATION MAY HAVE FAILED: Expected to restore {len(backup['markets'])} markets but only have {markets_after}")
            
        return restoration_successful
    except Exception as e:
        logger.error(f"Error restoring from backup: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def _create_emergency_backup(filename):
    """
    Create an emergency backup without triggering cleanup logic.
    For internal use during restoration process.
    
    Args:
        filename: Full path where backup should be saved
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Fetch data from collections
        markets_data = await markets_collection.find().to_list(length=None)
        queue_data = await classification_queue.find().to_list(length=None)
        
        # Custom JSON encoder to handle MongoDB data types
        class MongoJSONEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, datetime):
                    return {"$date": obj.isoformat()}
                if hasattr(obj, '__str__'):
                    return str(obj)
                return super().default(obj)
        
        # Process documents to make them JSON serializable
        def process_doc(doc):
            processed = {}
            for key, value in doc.items():
                if key == '_id':
                    processed[key] = str(value)
                elif isinstance(value, dict):
                    processed[key] = process_doc(value)
                elif isinstance(value, list):
                    processed[key] = [process_doc(item) if isinstance(item, dict) else item for item in value]
                else:
                    processed[key] = value
            return processed
        
        # Convert documents to JSON-serializable format
        markets_data = [process_doc(doc) for doc in markets_data]
        queue_data = [process_doc(doc) for doc in queue_data]
        
        # Create backup object
        backup = {
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "markets_count": len(markets_data),
            "queue_count": len(queue_data),
            "emergency": True,
            "markets": markets_data,
            "queue": queue_data
        }
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(backup, f, cls=MongoJSONEncoder, indent=2)
            
        file_size_mb = os.path.getsize(filename) / (1024 * 1024)
        logger.info(f"Created emergency backup at {filename} with {len(markets_data)} markets ({file_size_mb:.2f} MB)")
        return True
    except Exception as e:
        logger.error(f"Error creating emergency backup: {e}")
        return False

async def setup_automated_backups(interval_hours=1, max_backups=5):
    """
    Set up scheduled backups to run at regular intervals.
    
    Args:
        interval_hours: Hours between backups
        max_backups: Maximum number of backup files to keep
    """
    while True:
        try:
            # Create a backup and automatically clean up old ones
            await create_database_backup(max_backups=max_backups)
            
            # Wait for next backup interval
            await asyncio.sleep(interval_hours * 3600)
        except Exception as e:
            logger.error(f"Error in backup scheduler: {e}")
            await asyncio.sleep(3600)  # Wait an hour and try again

# Start automated backups
async def start_backup_scheduler():
    """Initialize the backup scheduler."""
    asyncio.create_task(setup_automated_backups())
