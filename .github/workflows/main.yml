name: CI/CD Pipeline for PolyVol

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.PRIVATE_KEY }}

      - name: Deploy To Digital Ocean
        run: |
          ssh -o StrictHostKeyChecking=no root@${{ secrets.SERVER_IP }} << 'EOF'
          cd /home/<USER>/
          sudo git pull origin main
          source venv/bin/activate
          cd /home/<USER>/venv/bin/
          sudo ./pip3 install -r /home/<USER>/requirements.txt
          sudo systemctl restart polyapi.service

          cd /home/<USER>/frontend
          npm i
          npm run build 
          sudo systemctl restart nginx 

          exit
          EOF

